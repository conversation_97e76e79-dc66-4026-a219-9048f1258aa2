# 降雨空间插值系统配置文件
# 请根据需要修改以下参数

# ==================== 基本路径配置 ====================
paths:
  # 项目根目录（自动检测，通常不需要修改）
  base_path: "D:/pythondata/jiangyuchazhi"
  
  # 输入数据目录
  input_dir: "input_another"
  
  # 站点信息文件
  stations_file: "stations.csv"
  
  # Delaunay-Molan分析结果文件
  delaunay_file: "Delaunay_Molan.csv"
  
  # 地形数据目录
  terrain_dir: "terrain/90"
  
  # 输出目录
  output_dir: "output"

# ==================== 运行模式配置 ====================
run_mode:
  # 运行模式选择：
  # "single_method_single_event": 单一方法处理单一洪水场次
  # "single_method_all_events": 单一方法处理所有洪水场次
  # "all_methods_single_event": 所有方法处理单一洪水场次
  # "all_methods_all_events": 所有方法处理所有洪水场次
  # "batch_processing": 批量处理模式
  mode: "single_method_single_event"
  
  # 指定洪水场次（当mode包含single_event时使用）
  target_flood_event: "2009-1"
  
  # 指定插值方法（当mode包含single_method时使用）
  # 可选: "IDW", "Kriging", "OI", "PRISM"
  target_method: "IDW"

# ==================== 插值方法配置 ====================
methods:
  # IDW (反距离权重插值)
  IDW:
    enabled: true
    # 距离权重指数
    power: 2.0
    # 搜索半径（度）
    search_radius: 0.5
    # 最小邻近点数
    min_neighbors: 3
    # 最大邻近点数
    max_neighbors: 12
    # 是否使用各向异性
    use_anisotropy: false
    # 各向异性参数（当use_anisotropy为true时使用）
    anisotropy_angle: 0.0
    anisotropy_ratio: 1.0
  
  # Kriging (克里金插值)
  Kriging:
    enabled: true
    # 变异函数类型: "spherical", "exponential", "gaussian", "linear"
    variogram_model: "spherical"
    # 是否启用参数优化
    enable_optimization: true
    # 搜索半径（度）
    search_radius: 0.8
    # 最小邻近点数
    min_neighbors: 3
    # 最大邻近点数
    max_neighbors: 15
    # 块金效应（nugget）初始值
    nugget: 0.0
    # 基台值（sill）初始值
    sill: 1.0
    # 变程（range）初始值
    range: 0.3
  
  # OI (最优插值)
  OI:
    enabled: true
    # 协方差函数类型: "exponential", "gaussian", "spherical"
    covariance_model: "exponential"
    # 是否启用参数优化
    enable_optimization: true
    # 搜索半径（度）
    search_radius: 0.8
    # 最小邻近点数
    min_neighbors: 3
    # 最大邻近点数
    max_neighbors: 15
    # 方差初始值
    variance: 1.0
    # 相关长度初始值
    correlation_length: 0.3
    # 观测误差方差
    observation_error_variance: 0.1
  
  # PRISM (参数回归独立斜率模型)
  PRISM:
    enabled: true
    # 是否启用参数优化
    enable_optimization: true
    # 搜索半径（度）
    search_radius: 1.0
    # 最小邻近点数
    min_neighbors: 4
    # 最大邻近点数
    max_neighbors: 20
    # 高程权重系数初始值
    elevation_weight: 0.5
    # 坡度权重系数初始值
    slope_weight: 0.3
    # 坡向权重系数初始值
    aspect_weight: 0.2
    # 距离衰减指数
    distance_decay: 2.0
    # 地形相似性阈值
    terrain_similarity_threshold: 0.7

# ==================== SCE-UA参数优化配置 ====================
optimization:
  # 是否启用SCE-UA优化（仅对Kriging、OI、PRISM有效）
  enable_sce_ua: true
  
  # 最大迭代次数
  max_iterations: 100
  
  # 最大函数评估次数
  max_function_evaluations: 2000
  
  # 最大优化时间（秒）
  max_time_seconds: 300
  
  # 复合形数量
  num_complexes: 5
  
  # 收敛阈值
  convergence_threshold: 1e-6
  
  # 是否使用并行计算
  use_parallel: true

# ==================== 数据处理配置 ====================
data_processing:
  # 最少非零雨量站点数（用于过滤时刻）
  min_nonzero_stations: 3
  
  # 是否输出栅格文件
  output_raster: true
  
  # 栅格输出格式: "asc", "tif"
  raster_format: "asc"
  
  # 是否输出流域平均雨量
  output_basin_average: true
  
  # 内存管理：处理多少个时刻后清理一次内存
  memory_cleanup_interval: 50

# ==================== 并行计算配置 ====================
parallel:
  # 是否启用并行计算
  enable_parallel: true
  
  # 并行进程数（0表示自动检测CPU核心数）
  num_processes: 0
  
  # 栅格插值时的分块大小
  chunk_size: 1000

# ==================== 评价指标配置 ====================
evaluation:
  # 要计算的评价指标
  metrics:
    - "MAE"      # 平均绝对误差
    - "RMSE"     # 均方根误差
    - "R2"       # 决定系数
    - "NSE"      # 纳什效率系数
    - "Bias"     # 偏差
    - "Relative_Bias"  # 相对偏差
    - "Correlation"    # 相关系数
  
  # 是否输出详细评价报告
  detailed_report: true
  
  # 是否保存评价结果到CSV文件
  save_to_csv: true

# ==================== 日志配置 ====================
logging:
  # 日志级别: "DEBUG", "INFO", "WARNING", "ERROR"
  level: "INFO"
  
  # 是否输出到控制台
  console: true
  
  # 是否输出到文件
  file: true
  
  # 日志文件路径
  log_file: "spatial_interpolation/logs/interpolation.log"
  
  # 日志格式
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# ==================== 调试配置 ====================
debug:
  # 是否启用调试模式
  enable: false
  
  # 调试时只处理前N个时刻
  max_time_steps: 10
  
  # 是否保存中间结果
  save_intermediate: false
  
  # 是否显示详细进度
  verbose_progress: true

# -*- coding: utf-8 -*-
"""
PRISM (参数回归独立斜率模型) 实现
Parameter-elevation Regressions on Independent Slopes Model
"""

import numpy as np
from typing import List, Tuple, Dict, Any, Optional
import logging
from scipy.optimize import minimize
from scipy.spatial.distance import cdist
from sklearn.linear_model import LinearRegression
import warnings
import gc

class PRISMInterpolator:
    """PRISM插值器"""
    
    def __init__(self,
                 search_radius: float = 1.0,
                 min_neighbors: int = 4,
                 max_neighbors: int = 20,
                 elevation_weight: float = 0.5,
                 slope_weight: float = 0.3,
                 aspect_weight: float = 0.2,
                 distance_decay: float = 2.0,
                 terrain_similarity_threshold: float = 0.7,
                 enable_optimization: bool = True):
        """
        初始化PRISM插值器
        
        Args:
            search_radius: 搜索半径（度）
            min_neighbors: 最小邻近点数
            max_neighbors: 最大邻近点数
            elevation_weight: 高程权重系数
            slope_weight: 坡度权重系数
            aspect_weight: 坡向权重系数
            distance_decay: 距离衰减指数
            terrain_similarity_threshold: 地形相似性阈值
            enable_optimization: 是否启用参数优化
        """
        self.search_radius = search_radius
        self.min_neighbors = min_neighbors
        self.max_neighbors = max_neighbors
        self.elevation_weight = elevation_weight
        self.slope_weight = slope_weight
        self.aspect_weight = aspect_weight
        self.distance_decay = distance_decay
        self.terrain_similarity_threshold = terrain_similarity_threshold
        self.enable_optimization = enable_optimization
        
        self.logger = logging.getLogger(__name__)
        
        # 缓存
        self._station_coords = None
        self._station_values = None
        self._station_terrain = None
        self._fitted_params = None
        
        # 地形数据
        self._terrain_data = None
    
    def set_terrain_data(self, dem_data: np.ndarray, slope_data: np.ndarray, 
                        aspect_data: np.ndarray, terrain_header: Dict[str, Any]):
        """
        设置地形数据
        
        Args:
            dem_data: DEM数据
            slope_data: 坡度数据
            aspect_data: 坡向数据
            terrain_header: 地形数据头信息
        """
        self._terrain_data = {
            'dem': dem_data,
            'slope': slope_data,
            'aspect': aspect_data,
            'header': terrain_header
        }
        self.logger.info("地形数据已设置")
    
    def fit(self, station_coords: np.ndarray, station_values: np.ndarray):
        """
        拟合PRISM插值器
        
        Args:
            station_coords: 站点坐标数组，形状为(n_stations, 2) [经度, 纬度]
            station_values: 站点观测值数组，形状为(n_stations,)
        """
        self._station_coords = np.array(station_coords)
        self._station_values = np.array(station_values)
        
        # 移除NaN值
        valid_mask = ~np.isnan(self._station_values)
        self._station_coords = self._station_coords[valid_mask]
        self._station_values = self._station_values[valid_mask]
        
        if len(self._station_values) < self.min_neighbors:
            raise ValueError(f"有效站点数量 {len(self._station_values)} 少于最小邻近点数 {self.min_neighbors}")
        
        # 提取站点地形信息
        self._extract_station_terrain()
        
        # 拟合参数
        if self.enable_optimization:
            self._fit_parameters()
        else:
            self._fitted_params = {
                'elevation_weight': self.elevation_weight,
                'slope_weight': self.slope_weight,
                'aspect_weight': self.aspect_weight,
                'distance_decay': self.distance_decay
            }
        
        self.logger.info(f"PRISM插值器已拟合，使用 {len(self._station_values)} 个有效站点")
        self.logger.info(f"PRISM参数: {self._fitted_params}")
    
    def predict(self, target_coords: np.ndarray) -> np.ndarray:
        """
        执行PRISM插值预测
        
        Args:
            target_coords: 目标点坐标数组，形状为(n_targets, 2) [经度, 纬度]
            
        Returns:
            插值结果数组
        """
        if self._station_coords is None or self._station_values is None:
            raise ValueError("插值器尚未拟合，请先调用fit方法")
        
        if self._terrain_data is None:
            raise ValueError("地形数据未设置，请先调用set_terrain_data方法")
        
        target_coords = np.array(target_coords)
        n_targets = len(target_coords)
        
        self.logger.info(f"开始PRISM插值，目标点数: {n_targets}")
        
        predictions = np.full(n_targets, np.nan)
        
        for i, target_coord in enumerate(target_coords):
            predictions[i] = self._interpolate_single_point(target_coord)
            
            # 每处理1000个点输出一次进度
            if (i + 1) % 1000 == 0:
                self.logger.info(f"已处理 {i + 1}/{n_targets} 个点")
        
        self.logger.info("PRISM插值完成")
        return predictions
    
    def _extract_station_terrain(self):
        """提取站点地形信息"""
        if self._terrain_data is None:
            self.logger.warning("地形数据未设置，无法提取站点地形信息")
            self._station_terrain = np.zeros((len(self._station_coords), 3))
            return
        
        n_stations = len(self._station_coords)
        self._station_terrain = np.zeros((n_stations, 3))  # [elevation, slope, aspect]
        
        header = self._terrain_data['header']
        
        for i, coord in enumerate(self._station_coords):
            # 将地理坐标转换为像素坐标
            row, col = self._coord_to_pixel(coord[0], coord[1], header)
            
            # 提取地形信息
            if (0 <= row < header['nrows'] and 0 <= col < header['ncols']):
                self._station_terrain[i, 0] = self._terrain_data['dem'][row, col]
                self._station_terrain[i, 1] = self._terrain_data['slope'][row, col]
                self._station_terrain[i, 2] = self._terrain_data['aspect'][row, col]
            else:
                # 如果坐标超出范围，使用默认值
                self._station_terrain[i, :] = [0, 0, 0]
        
        self.logger.info("站点地形信息提取完成")
    
    def _coord_to_pixel(self, lon: float, lat: float, header: Dict[str, Any]) -> Tuple[int, int]:
        """
        将地理坐标转换为像素坐标
        
        Args:
            lon: 经度
            lat: 纬度
            header: 栅格头信息
            
        Returns:
            (行号, 列号)
        """
        col = int((lon - header['xllcorner']) / header['cellsize'])
        row = int((header['yllcorner'] + header['nrows'] * header['cellsize'] - lat) / header['cellsize'])
        
        # 确保在有效范围内
        col = max(0, min(col, header['ncols'] - 1))
        row = max(0, min(row, header['nrows'] - 1))
        
        return row, col
    
    def _get_target_terrain(self, target_coord: np.ndarray) -> np.ndarray:
        """
        获取目标点地形信息
        
        Args:
            target_coord: 目标点坐标
            
        Returns:
            地形信息数组 [elevation, slope, aspect]
        """
        if self._terrain_data is None:
            return np.array([0, 0, 0])
        
        header = self._terrain_data['header']
        row, col = self._coord_to_pixel(target_coord[0], target_coord[1], header)
        
        terrain = np.zeros(3)
        if (0 <= row < header['nrows'] and 0 <= col < header['ncols']):
            terrain[0] = self._terrain_data['dem'][row, col]
            terrain[1] = self._terrain_data['slope'][row, col]
            terrain[2] = self._terrain_data['aspect'][row, col]
        
        return terrain
    
    def _fit_parameters(self):
        """拟合PRISM参数"""
        self.logger.info("开始拟合PRISM参数")
        
        # 设置参数边界
        bounds = [
            (0.1, 1.0),  # elevation_weight
            (0.0, 1.0),  # slope_weight
            (0.0, 1.0),  # aspect_weight
            (1.0, 5.0)   # distance_decay
        ]
        
        # 初始参数
        initial_params = [
            self.elevation_weight,
            self.slope_weight,
            self.aspect_weight,
            self.distance_decay
        ]
        
        # 目标函数：交叉验证误差
        def objective(params):
            elevation_weight, slope_weight, aspect_weight, distance_decay = params
            
            # 留一法交叉验证
            errors = []
            for i in range(len(self._station_coords)):
                # 排除第i个站点
                train_coords = np.delete(self._station_coords, i, axis=0)
                train_values = np.delete(self._station_values, i)
                train_terrain = np.delete(self._station_terrain, i, axis=0)
                
                # 预测第i个站点
                target_coord = self._station_coords[i]
                target_terrain = self._station_terrain[i]
                observed_value = self._station_values[i]
                
                predicted_value = self._prism_interpolate(
                    target_coord, target_terrain, train_coords, train_values, train_terrain,
                    elevation_weight, slope_weight, aspect_weight, distance_decay
                )
                
                if not np.isnan(predicted_value):
                    errors.append((observed_value - predicted_value) ** 2)
            
            return np.mean(errors) if errors else np.inf
        
        # 优化
        try:
            result = minimize(
                objective,
                initial_params,
                bounds=bounds,
                method='L-BFGS-B'
            )
            
            if result.success:
                self._fitted_params = {
                    'elevation_weight': result.x[0],
                    'slope_weight': result.x[1],
                    'aspect_weight': result.x[2],
                    'distance_decay': result.x[3]
                }
                self.logger.info("PRISM参数拟合成功")
            else:
                self.logger.warning("PRISM参数拟合失败，使用默认参数")
                self._fitted_params = {
                    'elevation_weight': self.elevation_weight,
                    'slope_weight': self.slope_weight,
                    'aspect_weight': self.aspect_weight,
                    'distance_decay': self.distance_decay
                }
                
        except Exception as e:
            self.logger.error(f"PRISM参数拟合出错: {e}")
            self._fitted_params = {
                'elevation_weight': self.elevation_weight,
                'slope_weight': self.slope_weight,
                'aspect_weight': self.aspect_weight,
                'distance_decay': self.distance_decay
            }

    def _interpolate_single_point(self, target_coord: np.ndarray) -> float:
        """
        对单个点进行PRISM插值

        Args:
            target_coord: 目标点坐标 [经度, 纬度]

        Returns:
            插值结果
        """
        # 获取目标点地形信息
        target_terrain = self._get_target_terrain(target_coord)

        # 执行PRISM插值
        return self._prism_interpolate(
            target_coord, target_terrain,
            self._station_coords, self._station_values, self._station_terrain,
            self._fitted_params['elevation_weight'],
            self._fitted_params['slope_weight'],
            self._fitted_params['aspect_weight'],
            self._fitted_params['distance_decay']
        )

    def _prism_interpolate(self, target_coord: np.ndarray, target_terrain: np.ndarray,
                          station_coords: np.ndarray, station_values: np.ndarray,
                          station_terrain: np.ndarray, elevation_weight: float,
                          slope_weight: float, aspect_weight: float,
                          distance_decay: float) -> float:
        """
        执行PRISM插值计算

        Args:
            target_coord: 目标点坐标
            target_terrain: 目标点地形信息
            station_coords: 站点坐标
            station_values: 站点值
            station_terrain: 站点地形信息
            elevation_weight: 高程权重
            slope_weight: 坡度权重
            aspect_weight: 坡向权重
            distance_decay: 距离衰减指数

        Returns:
            插值结果
        """
        # 计算距离
        distances = cdist([target_coord], station_coords)[0]

        # 筛选邻近点
        valid_indices = distances <= self.search_radius
        if np.sum(valid_indices) < self.min_neighbors:
            # 如果搜索半径内点数不足，选择最近的点
            sorted_indices = np.argsort(distances)
            if len(sorted_indices) >= self.min_neighbors:
                valid_indices = sorted_indices[:self.min_neighbors]
            else:
                valid_indices = sorted_indices
        else:
            # 限制最大邻近点数
            valid_distances = distances[valid_indices]
            if len(valid_distances) > self.max_neighbors:
                sorted_valid_indices = np.argsort(valid_distances)[:self.max_neighbors]
                temp_indices = np.where(valid_indices)[0]
                valid_indices = np.zeros_like(valid_indices, dtype=bool)
                valid_indices[temp_indices[sorted_valid_indices]] = True

        if isinstance(valid_indices, np.ndarray) and valid_indices.dtype == bool:
            neighbor_coords = station_coords[valid_indices]
            neighbor_values = station_values[valid_indices]
            neighbor_terrain = station_terrain[valid_indices]
            neighbor_distances = distances[valid_indices]
        else:
            neighbor_coords = station_coords[valid_indices]
            neighbor_values = station_values[valid_indices]
            neighbor_terrain = station_terrain[valid_indices]
            neighbor_distances = distances[valid_indices]

        if len(neighbor_coords) == 0:
            return np.nan

        # 计算地形相似性权重
        terrain_weights = self._calculate_terrain_similarity(
            target_terrain, neighbor_terrain,
            elevation_weight, slope_weight, aspect_weight
        )

        # 计算距离权重
        distance_weights = 1.0 / (neighbor_distances ** distance_decay + 1e-10)

        # 组合权重
        combined_weights = terrain_weights * distance_weights

        # 筛选相似地形的站点
        similarity_mask = terrain_weights >= self.terrain_similarity_threshold
        if np.sum(similarity_mask) >= 3:
            # 如果有足够的相似地形站点，只使用这些站点
            combined_weights = combined_weights * similarity_mask

        # 归一化权重
        weights_sum = np.sum(combined_weights)
        if weights_sum > 0:
            normalized_weights = combined_weights / weights_sum

            # 计算加权平均
            prediction = np.sum(normalized_weights * neighbor_values)

            # 应用高程梯度修正
            prediction = self._apply_elevation_correction(
                prediction, target_terrain[0], neighbor_terrain[:, 0],
                neighbor_values, normalized_weights
            )

            return prediction
        else:
            return np.nan

    def _calculate_terrain_similarity(self, target_terrain: np.ndarray,
                                    neighbor_terrain: np.ndarray,
                                    elevation_weight: float, slope_weight: float,
                                    aspect_weight: float) -> np.ndarray:
        """
        计算地形相似性权重

        Args:
            target_terrain: 目标点地形信息
            neighbor_terrain: 邻近点地形信息
            elevation_weight: 高程权重
            slope_weight: 坡度权重
            aspect_weight: 坡向权重

        Returns:
            地形相似性权重数组
        """
        n_neighbors = len(neighbor_terrain)
        similarities = np.zeros(n_neighbors)

        for i in range(n_neighbors):
            # 高程相似性
            elev_diff = abs(target_terrain[0] - neighbor_terrain[i, 0])
            elev_similarity = np.exp(-elev_diff / 100.0)  # 100m为特征尺度

            # 坡度相似性
            slope_diff = abs(target_terrain[1] - neighbor_terrain[i, 1])
            slope_similarity = np.exp(-slope_diff / 10.0)  # 10度为特征尺度

            # 坡向相似性
            aspect_diff = self._calculate_aspect_difference(
                target_terrain[2], neighbor_terrain[i, 2]
            )
            aspect_similarity = np.exp(-aspect_diff / 45.0)  # 45度为特征尺度

            # 组合相似性
            similarities[i] = (
                elevation_weight * elev_similarity +
                slope_weight * slope_similarity +
                aspect_weight * aspect_similarity
            ) / (elevation_weight + slope_weight + aspect_weight)

        return similarities

    def _calculate_aspect_difference(self, aspect1: float, aspect2: float) -> float:
        """
        计算坡向差异（考虑圆周性）

        Args:
            aspect1: 坡向1（度）
            aspect2: 坡向2（度）

        Returns:
            坡向差异（度）
        """
        diff = abs(aspect1 - aspect2)
        return min(diff, 360 - diff)

    def _apply_elevation_correction(self, base_prediction: float, target_elevation: float,
                                  neighbor_elevations: np.ndarray, neighbor_values: np.ndarray,
                                  weights: np.ndarray) -> float:
        """
        应用高程梯度修正

        Args:
            base_prediction: 基础预测值
            target_elevation: 目标点高程
            neighbor_elevations: 邻近点高程
            neighbor_values: 邻近点值
            weights: 权重

        Returns:
            修正后的预测值
        """
        # 计算加权平均高程
        weighted_elevation = np.sum(weights * neighbor_elevations)

        # 计算高程梯度
        elevation_diffs = neighbor_elevations - weighted_elevation
        value_diffs = neighbor_values - base_prediction

        # 使用加权线性回归计算梯度
        if len(elevation_diffs) > 1 and np.std(elevation_diffs) > 1e-6:
            gradient = np.sum(weights * elevation_diffs * value_diffs) / np.sum(weights * elevation_diffs ** 2)

            # 应用梯度修正
            elevation_correction = gradient * (target_elevation - weighted_elevation)
            corrected_prediction = base_prediction + elevation_correction

            return corrected_prediction
        else:
            return base_prediction

    def get_parameters(self) -> Dict[str, Any]:
        """
        获取当前参数

        Returns:
            参数字典
        """
        params = {
            'search_radius': self.search_radius,
            'min_neighbors': self.min_neighbors,
            'max_neighbors': self.max_neighbors,
            'terrain_similarity_threshold': self.terrain_similarity_threshold,
            'enable_optimization': self.enable_optimization
        }

        if self._fitted_params:
            params.update(self._fitted_params)
        else:
            params.update({
                'elevation_weight': self.elevation_weight,
                'slope_weight': self.slope_weight,
                'aspect_weight': self.aspect_weight,
                'distance_decay': self.distance_decay
            })

        return params

    def set_parameters(self, **params):
        """
        设置参数

        Args:
            **params: 参数字典
        """
        for key, value in params.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def clear_cache(self):
        """清理缓存"""
        self._station_coords = None
        self._station_values = None
        self._station_terrain = None
        self._fitted_params = None
        gc.collect()

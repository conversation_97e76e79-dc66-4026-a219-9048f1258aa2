# -*- coding: utf-8 -*-
"""
运行所有命令的演示脚本
展示降雨空间插值系统的各种功能
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def run_command(command, description, timeout=300):
    """
    运行命令并显示结果
    
    Args:
        command: 要运行的命令
        description: 命令描述
        timeout: 超时时间（秒）
    """
    print(f"\n{'='*60}")
    print(f"运行: {description}")
    print(f"命令: {command}")
    print(f"{'='*60}")
    
    try:
        start_time = time.time()
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            encoding='utf-8'
        )
        
        duration = time.time() - start_time
        
        print(f"返回码: {result.returncode}")
        print(f"执行时间: {duration:.2f} 秒")
        
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print(f"命令超时 ({timeout} 秒)")
        return False
    except Exception as e:
        print(f"命令执行失败: {e}")
        return False

def main():
    """主函数"""
    print("降雨空间插值系统 - 完整功能演示")
    print("=" * 60)
    
    # 确保在正确的目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    commands = [
        # 1. 系统测试
        {
            'command': 'python test_system.py',
            'description': '系统基础功能测试',
            'timeout': 60
        },
        
        # 2. 查看洪水场次
        {
            'command': 'python main.py --list-events',
            'description': '查看可用洪水场次',
            'timeout': 30
        },
        
        # 3. 运行IDW插值 - 单个场次
        {
            'command': 'python main.py --method IDW --flood-event 2009-1',
            'description': 'IDW插值 - 处理2009-1洪水场次',
            'timeout': 180
        },
        
        # 4. 查看配置信息
        {
            'command': 'python -c "from config.config_manager import ConfigManager; c=ConfigManager(); print(f\'启用方法: {c.get_enabled_methods()}\'); print(f\'运行模式: {c.get_run_mode()}\')"',
            'description': '查看当前配置信息',
            'timeout': 30
        },
        
        # 5. 运行主程序测试
        {
            'command': 'python main.py --test',
            'description': '通过主程序运行系统测试',
            'timeout': 60
        }
    ]
    
    # 可选的高级命令（需要更长时间）
    advanced_commands = [
        # 6. 批量处理示例
        {
            'command': 'python batch_processor.py --methods IDW --events 2009-1',
            'description': '批量处理器演示',
            'timeout': 300
        },
        
        # 7. 运行所有启用的方法
        {
            'command': 'python main.py --method all --flood-event 2009-1',
            'description': '运行所有启用的插值方法',
            'timeout': 600
        }
    ]
    
    # 运行基础命令
    success_count = 0
    total_count = len(commands)
    
    for i, cmd_info in enumerate(commands, 1):
        print(f"\n进度: {i}/{total_count}")
        success = run_command(
            cmd_info['command'], 
            cmd_info['description'], 
            cmd_info['timeout']
        )
        
        if success:
            success_count += 1
            print("✅ 成功")
        else:
            print("❌ 失败")
    
    # 询问是否运行高级命令
    print(f"\n{'='*60}")
    print(f"基础命令完成: {success_count}/{total_count} 成功")
    print(f"{'='*60}")
    
    response = input("\n是否运行高级命令？(这些命令需要更长时间) [y/N]: ").strip().lower()
    
    if response in ['y', 'yes']:
        print("\n运行高级命令...")
        
        for i, cmd_info in enumerate(advanced_commands, 1):
            print(f"\n高级命令进度: {i}/{len(advanced_commands)}")
            success = run_command(
                cmd_info['command'], 
                cmd_info['description'], 
                cmd_info['timeout']
            )
            
            if success:
                success_count += 1
                print("✅ 成功")
            else:
                print("❌ 失败")
        
        total_count += len(advanced_commands)
    
    # 最终总结
    print(f"\n{'='*60}")
    print("演示完成!")
    print(f"总命令数: {total_count}")
    print(f"成功命令数: {success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    print(f"{'='*60}")
    
    # 显示输出文件
    output_dir = Path("../output")
    if output_dir.exists():
        print("\n生成的输出文件:")
        for root, dirs, files in os.walk(output_dir):
            level = root.replace(str(output_dir), '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files[:5]:  # 只显示前5个文件
                print(f"{subindent}{file}")
            if len(files) > 5:
                print(f"{subindent}... 还有 {len(files)-5} 个文件")
    
    print("\n系统功能演示完成！")
    print("详细使用说明请参考: docs/README.md 和 docs/USAGE_EXAMPLES.md")

if __name__ == '__main__':
    main()

# -*- coding: utf-8 -*-
"""
批量处理模块
支持批量处理多个洪水场次和多种插值方法
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional
import time
import gc

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.config_manager import ConfigManager
from common.data_processor import DataProcessor
from common.raster_utils import RasterUtils
from common.evaluation_metrics import EvaluationMetrics

class BatchProcessor:
    """批量处理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化批量处理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = ConfigManager(config_file)
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.data_processor = DataProcessor(self.config.get('paths.base_path'))
        self.raster_utils = RasterUtils()
        self.evaluator = EvaluationMetrics()
        
        # 结果存储
        self.batch_results = []
        
        # 方法运行器映射
        self.method_runners = {
            'IDW': self._run_idw_method,
            'Kriging': self._run_kriging_method,
            'OI': self._run_oi_method,
            'PRISM': self._run_prism_method
        }
    
    def run_batch_processing(self, methods: List[str] = None, 
                           flood_events: List[str] = None) -> Dict[str, Any]:
        """
        运行批量处理
        
        Args:
            methods: 要运行的方法列表，如果为None则使用配置中启用的方法
            flood_events: 要处理的洪水场次列表，如果为None则处理所有场次
            
        Returns:
            批量处理结果
        """
        start_time = time.time()
        self.logger.info("开始批量处理")
        
        # 确定要运行的方法
        if methods is None:
            methods = self.config.get_enabled_methods()
        
        # 确定要处理的洪水场次
        if flood_events is None:
            flood_events = self.data_processor.get_flood_events()
        
        self.logger.info(f"批量处理配置:")
        self.logger.info(f"  方法: {methods}")
        self.logger.info(f"  洪水场次: {len(flood_events)} 个")
        
        # 创建批量结果目录
        batch_output_dir = os.path.join(
            self.config.get('paths.output_dir'),
            'batch_results',
            datetime.now().strftime('%Y%m%d_%H%M%S')
        )
        os.makedirs(batch_output_dir, exist_ok=True)
        
        # 处理每种方法
        method_results = {}
        
        for method in methods:
            if method in self.method_runners:
                self.logger.info(f"开始处理方法: {method}")
                method_start_time = time.time()
                
                try:
                    method_result = self._run_method_batch(method, flood_events)
                    method_results[method] = method_result
                    
                    method_duration = time.time() - method_start_time
                    self.logger.info(f"方法 {method} 处理完成，耗时: {method_duration:.2f} 秒")
                    
                except Exception as e:
                    self.logger.error(f"方法 {method} 处理失败: {e}")
                    method_results[method] = {
                        'status': 'error',
                        'error_message': str(e),
                        'processed_events': 0
                    }
                
                # 清理内存
                gc.collect()
            else:
                self.logger.warning(f"不支持的方法: {method}")
        
        # 生成批量处理报告
        batch_result = self._generate_batch_report(
            method_results, flood_events, batch_output_dir
        )
        
        total_duration = time.time() - start_time
        batch_result['total_duration'] = total_duration
        
        self.logger.info(f"批量处理完成，总耗时: {total_duration:.2f} 秒")
        
        return batch_result
    
    def _run_method_batch(self, method: str, flood_events: List[str]) -> Dict[str, Any]:
        """
        运行单个方法的批量处理
        
        Args:
            method: 方法名称
            flood_events: 洪水场次列表
            
        Returns:
            方法处理结果
        """
        method_results = []
        successful_events = 0
        
        for i, flood_event in enumerate(flood_events):
            self.logger.info(f"处理 {method} - {flood_event} ({i+1}/{len(flood_events)})")
            
            try:
                # 运行单个洪水场次
                event_result = self.method_runners[method](flood_event)
                method_results.append(event_result)
                
                if event_result.get('status') == 'completed':
                    successful_events += 1
                
                # 定期清理内存
                if (i + 1) % self.config.get('data_processing.memory_cleanup_interval', 10) == 0:
                    self.data_processor.clear_cache()
                    self.raster_utils.clear_memory()
                    gc.collect()
                    
            except Exception as e:
                self.logger.error(f"处理 {method} - {flood_event} 失败: {e}")
                method_results.append({
                    'flood_event': flood_event,
                    'method': method,
                    'status': 'error',
                    'error_message': str(e)
                })
        
        return {
            'method': method,
            'status': 'completed',
            'processed_events': successful_events,
            'total_events': len(flood_events),
            'event_results': method_results
        }
    
    def _run_idw_method(self, flood_event: str) -> Dict[str, Any]:
        """运行IDW方法"""
        from IDW_python.run_idw import IDWRunner
        
        runner = IDWRunner()
        runner.config = self.config
        return runner.run_single_event(flood_event)
    
    def _run_kriging_method(self, flood_event: str) -> Dict[str, Any]:
        """运行Kriging方法"""
        # TODO: 实现Kriging运行器
        self.logger.warning("Kriging方法尚未完全实现")
        return {
            'flood_event': flood_event,
            'method': 'Kriging',
            'status': 'not_implemented'
        }
    
    def _run_oi_method(self, flood_event: str) -> Dict[str, Any]:
        """运行OI方法"""
        # TODO: 实现OI运行器
        self.logger.warning("OI方法尚未完全实现")
        return {
            'flood_event': flood_event,
            'method': 'OI',
            'status': 'not_implemented'
        }
    
    def _run_prism_method(self, flood_event: str) -> Dict[str, Any]:
        """运行PRISM方法"""
        # TODO: 实现PRISM运行器
        self.logger.warning("PRISM方法尚未完全实现")
        return {
            'flood_event': flood_event,
            'method': 'PRISM',
            'status': 'not_implemented'
        }
    
    def _generate_batch_report(self, method_results: Dict[str, Any], 
                             flood_events: List[str], 
                             output_dir: str) -> Dict[str, Any]:
        """
        生成批量处理报告
        
        Args:
            method_results: 方法处理结果
            flood_events: 洪水场次列表
            output_dir: 输出目录
            
        Returns:
            批量处理结果
        """
        # 收集所有评价指标
        all_metrics = []
        
        for method, result in method_results.items():
            if result['status'] == 'completed' and 'event_results' in result:
                for event_result in result['event_results']:
                    if (event_result.get('status') == 'completed' and 
                        'station_results' in event_result):
                        
                        for station_result in event_result['station_results']:
                            metrics = station_result.get('metrics', {})
                            metric_row = {
                                'method': method,
                                'flood_event': event_result['flood_event'],
                                'station_id': station_result['station_id'],
                                'station_name': station_result['station_name'],
                                'longitude': station_result['longitude'],
                                'latitude': station_result['latitude'],
                                'valid_points': station_result['valid_points']
                            }
                            metric_row.update(metrics)
                            all_metrics.append(metric_row)
        
        # 保存详细结果
        if all_metrics:
            metrics_df = pd.DataFrame(all_metrics)
            metrics_file = os.path.join(output_dir, 'batch_detailed_results.csv')
            metrics_df.to_csv(metrics_file, index=False, encoding='utf-8')
            
            # 生成统计摘要
            summary_stats = self._calculate_batch_statistics(metrics_df)
            
            # 保存统计摘要
            summary_file = os.path.join(output_dir, 'batch_summary_statistics.csv')
            summary_stats.to_csv(summary_file, index=False, encoding='utf-8')
            
            self.logger.info(f"批量处理报告已保存到: {output_dir}")
        
        # 生成处理摘要
        processing_summary = []
        for method, result in method_results.items():
            summary_row = {
                'method': method,
                'status': result['status'],
                'processed_events': result.get('processed_events', 0),
                'total_events': result.get('total_events', 0),
                'success_rate': (result.get('processed_events', 0) / 
                               max(result.get('total_events', 1), 1) * 100)
            }
            processing_summary.append(summary_row)
        
        processing_df = pd.DataFrame(processing_summary)
        processing_file = os.path.join(output_dir, 'batch_processing_summary.csv')
        processing_df.to_csv(processing_file, index=False, encoding='utf-8')
        
        return {
            'status': 'completed',
            'output_directory': output_dir,
            'method_results': method_results,
            'total_flood_events': len(flood_events),
            'total_metrics_records': len(all_metrics),
            'files': {
                'detailed_results': metrics_file if all_metrics else None,
                'summary_statistics': summary_file if all_metrics else None,
                'processing_summary': processing_file
            }
        }
    
    def _calculate_batch_statistics(self, metrics_df: pd.DataFrame) -> pd.DataFrame:
        """
        计算批量处理统计信息
        
        Args:
            metrics_df: 指标数据框
            
        Returns:
            统计信息数据框
        """
        stats_data = []
        
        # 按方法分组统计
        for method in metrics_df['method'].unique():
            method_data = metrics_df[metrics_df['method'] == method]
            
            for metric in ['MAE', 'RMSE', 'R2', 'NSE', 'Bias', 'Relative_Bias', 'Correlation']:
                if metric in method_data.columns:
                    values = method_data[metric].dropna()
                    if len(values) > 0:
                        stats_row = {
                            'method': method,
                            'metric': metric,
                            'count': len(values),
                            'mean': values.mean(),
                            'std': values.std(),
                            'min': values.min(),
                            'max': values.max(),
                            'median': values.median(),
                            'q25': values.quantile(0.25),
                            'q75': values.quantile(0.75)
                        }
                        stats_data.append(stats_row)
        
        return pd.DataFrame(stats_data)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='批量处理器')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--methods', type=str, nargs='+', 
                       choices=['IDW', 'Kriging', 'OI', 'PRISM', 'all'],
                       help='要运行的方法')
    parser.add_argument('--events', type=str, nargs='+', help='要处理的洪水场次')
    
    args = parser.parse_args()
    
    try:
        # 创建批量处理器
        processor = BatchProcessor(args.config)
        
        # 确定方法
        methods = None
        if args.methods:
            if 'all' in args.methods:
                methods = processor.config.get_enabled_methods()
            else:
                methods = args.methods
        
        # 运行批量处理
        result = processor.run_batch_processing(methods, args.events)
        
        print("批量处理完成!")
        print(f"输出目录: {result['output_directory']}")
        print(f"总洪水场次: {result['total_flood_events']}")
        print(f"总指标记录: {result['total_metrics_records']}")
        
        # 显示方法处理结果
        for method, method_result in result['method_results'].items():
            status = method_result['status']
            if status == 'completed':
                processed = method_result.get('processed_events', 0)
                total = method_result.get('total_events', 0)
                print(f"{method}: {processed}/{total} 个场次成功处理")
            else:
                print(f"{method}: {status}")
        
    except Exception as e:
        print(f"批量处理出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()

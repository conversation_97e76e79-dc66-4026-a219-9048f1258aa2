# -*- coding: utf-8 -*-
"""
系统测试脚本
用于验证各个模块的基本功能
"""

import os
import sys
import numpy as np
import pandas as pd
import logging
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from common.data_processor import DataProcessor
from common.raster_utils import RasterUtils
from common.evaluation_metrics import EvaluationMetrics
from config.config_manager import ConfigManager
from IDW_python.idw_interpolator import IDWInterpolator

def test_config_manager():
    """测试配置管理器"""
    print("=== 测试配置管理器 ===")
    
    try:
        config = ConfigManager()
        print(f"基础路径: {config.get('paths.base_path')}")
        print(f"运行模式: {config.get_run_mode()}")
        print(f"启用的方法: {config.get_enabled_methods()}")
        print("配置管理器测试通过 ✓")
        return config
    except Exception as e:
        print(f"配置管理器测试失败: {e}")
        return None

def test_data_processor(config):
    """测试数据处理器"""
    print("\n=== 测试数据处理器 ===")
    
    try:
        processor = DataProcessor(config.get('paths.base_path'))
        
        # 测试加载站点信息
        stations = processor.load_stations()
        print(f"加载站点数量: {len(stations)}")
        
        # 测试获取洪水场次
        flood_events = processor.get_flood_events()
        print(f"洪水场次数量: {len(flood_events)}")
        
        if flood_events:
            # 测试加载第一个洪水场次的数据
            first_event = flood_events[0]
            print(f"测试洪水场次: {first_event}")
            
            rainfall_data = processor.load_rainfall_data(first_event)
            print(f"雨量数据站点数: {len(rainfall_data)}")
            
            # 测试插值信息
            interp_info = processor.get_interpolation_info(first_event)
            print(f"插值信息条数: {len(interp_info)}")
            
        print("数据处理器测试通过 ✓")
        return processor
        
    except Exception as e:
        print(f"数据处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_raster_utils(config):
    """测试栅格工具"""
    print("\n=== 测试栅格工具 ===")
    
    try:
        raster_utils = RasterUtils()
        
        # 测试读取掩膜文件
        terrain_dir = config.get('paths.terrain_dir')
        mask_file = os.path.join(terrain_dir, 'mask.asc')
        
        if os.path.exists(mask_file):
            mask_data, mask_header = raster_utils.read_asc_file(mask_file)
            print(f"掩膜数据尺寸: {mask_data.shape}")
            print(f"掩膜头信息: {mask_header}")
            
            # 测试坐标生成
            grid_x, grid_y = raster_utils.get_coordinates_from_header(mask_header)
            print(f"坐标网格尺寸: {grid_x.shape}")
            
            print("栅格工具测试通过 ✓")
            return raster_utils, mask_data, mask_header
        else:
            print(f"掩膜文件不存在: {mask_file}")
            return None, None, None
            
    except Exception as e:
        print(f"栅格工具测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def test_evaluation_metrics():
    """测试评价指标"""
    print("\n=== 测试评价指标 ===")
    
    try:
        evaluator = EvaluationMetrics()
        
        # 生成测试数据
        np.random.seed(42)
        observed = np.random.rand(100) * 10
        predicted = observed + np.random.normal(0, 1, 100)  # 添加噪声
        
        # 计算指标
        metrics = evaluator.calculate_all_metrics(observed, predicted)
        
        print("评价指标结果:")
        for key, value in metrics.items():
            print(f"  {key}: {value:.4f}")
        
        print("评价指标测试通过 ✓")
        return evaluator
        
    except Exception as e:
        print(f"评价指标测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_idw_interpolator():
    """测试IDW插值器"""
    print("\n=== 测试IDW插值器 ===")
    
    try:
        # 创建测试数据
        np.random.seed(42)
        n_stations = 10
        station_coords = np.random.rand(n_stations, 2) * 2  # 经纬度范围0-2
        station_values = np.random.rand(n_stations) * 20   # 雨量范围0-20
        
        # 创建插值器
        interpolator = IDWInterpolator(
            power=2.0,
            search_radius=1.0,
            min_neighbors=3,
            max_neighbors=8,
            use_parallel=False  # 测试时不使用并行
        )
        
        # 拟合
        interpolator.fit(station_coords, station_values)
        
        # 预测
        target_coords = np.random.rand(5, 2) * 2
        predictions = interpolator.predict(target_coords)
        
        print(f"站点坐标: {station_coords[:3]}")  # 显示前3个
        print(f"站点值: {station_values[:3]}")
        print(f"目标坐标: {target_coords[:3]}")
        print(f"预测值: {predictions[:3]}")
        
        # 检查结果
        assert len(predictions) == len(target_coords)
        assert not np.all(np.isnan(predictions))
        
        print("IDW插值器测试通过 ✓")
        return interpolator
        
    except Exception as e:
        print(f"IDW插值器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_simple_interpolation(config, processor):
    """测试简单插值流程"""
    print("\n=== 测试简单插值流程 ===")
    
    try:
        # 获取第一个洪水场次
        flood_events = processor.get_flood_events()
        if not flood_events:
            print("没有洪水场次数据")
            return False
        
        first_event = flood_events[0]
        print(f"使用洪水场次: {first_event}")
        
        # 加载数据
        rainfall_data = processor.load_rainfall_data(first_event)
        interp_info = processor.get_interpolation_info(first_event)
        
        if interp_info.empty:
            print("没有插值信息")
            return False
        
        # 选择第一个待插值站点
        first_station = interp_info.iloc[0]
        target_station = first_station['target_station']
        target_lon = first_station['target_longitude']
        target_lat = first_station['target_latitude']
        
        print(f"目标站点: {target_station} ({target_lon:.4f}, {target_lat:.4f})")
        
        # 解析插值站点
        interp_stations = processor.parse_interp_stations(
            first_station['interp_stations_info']
        )
        print(f"插值站点数量: {len(interp_stations)}")
        
        if len(interp_stations) < 3:
            print("插值站点数量不足")
            return False
        
        # 获取插值站点坐标和值
        interp_coords = []
        interp_values = []
        
        # 获取第一个时刻的数据
        if target_station in rainfall_data:
            target_data = rainfall_data[target_station]
            if not target_data.empty:
                first_time = target_data['时间'].iloc[0]
                print(f"测试时刻: {first_time}")
                
                for station_name, station_id in interp_stations:
                    coords = processor.get_station_coordinates(station_id)
                    if coords and station_id in rainfall_data:
                        station_data = rainfall_data[station_id]
                        time_data = station_data[station_data['时间'] == first_time]
                        if not time_data.empty:
                            interp_coords.append(coords)
                            interp_values.append(time_data['雨量'].iloc[0])
                
                if len(interp_coords) >= 3:
                    # 执行IDW插值
                    interpolator = IDWInterpolator(use_parallel=False)
                    interpolator.fit(np.array(interp_coords), np.array(interp_values))
                    
                    prediction = interpolator.predict([[target_lon, target_lat]])[0]
                    
                    # 获取观测值
                    observed = target_data[target_data['时间'] == first_time]['雨量'].iloc[0]
                    
                    print(f"观测值: {observed:.2f}")
                    print(f"插值值: {prediction:.2f}")
                    print(f"误差: {abs(observed - prediction):.2f}")
                    
                    print("简单插值流程测试通过 ✓")
                    return True
        
        print("无法完成插值测试")
        return False
        
    except Exception as e:
        print(f"简单插值流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始系统测试...")
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 测试各个模块
    config = test_config_manager()
    if not config:
        return
    
    processor = test_data_processor(config)
    if not processor:
        return
    
    raster_utils, mask_data, mask_header = test_raster_utils(config)
    
    evaluator = test_evaluation_metrics()
    
    idw_interpolator = test_idw_interpolator()
    
    # 测试完整流程
    if processor:
        test_simple_interpolation(config, processor)
    
    print("\n=== 系统测试完成 ===")

if __name__ == '__main__':
    main()

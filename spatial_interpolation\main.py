# -*- coding: utf-8 -*-
"""
降雨空间插值系统主程序
支持PRISM、Kriging、O<PERSON>、IDW四种插值方法
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Any

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.config_manager import ConfigManager
from common.data_processor import DataProcessor

def setup_logging(config: ConfigManager):
    """设置日志"""
    # 日志已在ConfigManager中设置
    pass

def run_idw_interpolation(config: ConfigManager, flood_events: List[str] = None) -> Dict[str, Any]:
    """
    运行IDW插值
    
    Args:
        config: 配置管理器
        flood_events: 洪水场次列表，如果为None则根据配置决定
        
    Returns:
        运行结果
    """
    from IDW_python.run_idw import IDWRunner
    
    logger = logging.getLogger(__name__)
    logger.info("开始IDW插值")
    
    runner = IDWRunner()
    runner.config = config
    
    if flood_events is None:
        # 根据配置决定运行模式
        run_mode = config.get_run_mode()
        target_event = config.get_target_flood_event()
        
        if 'all_events' in run_mode:
            results = runner.run_all_events()
        elif target_event:
            results = [runner.run_single_event(target_event)]
        else:
            # 默认运行第一个洪水场次
            processor = DataProcessor(config.get('paths.base_path'))
            all_events = processor.get_flood_events()
            if all_events:
                results = [runner.run_single_event(all_events[0])]
            else:
                logger.error("没有找到洪水场次数据")
                return {'status': 'error', 'message': '没有找到洪水场次数据'}
    else:
        # 运行指定的洪水场次
        results = []
        for event in flood_events:
            results.append(runner.run_single_event(event))
    
    logger.info("IDW插值完成")
    return {'method': 'IDW', 'status': 'completed', 'results': results}

def run_kriging_interpolation(config: ConfigManager, flood_events: List[str] = None) -> Dict[str, Any]:
    """
    运行Kriging插值
    
    Args:
        config: 配置管理器
        flood_events: 洪水场次列表
        
    Returns:
        运行结果
    """
    logger = logging.getLogger(__name__)
    logger.info("Kriging插值功能正在开发中...")
    
    # TODO: 实现Kriging插值运行器
    return {'method': 'Kriging', 'status': 'not_implemented'}

def run_oi_interpolation(config: ConfigManager, flood_events: List[str] = None) -> Dict[str, Any]:
    """
    运行OI插值
    
    Args:
        config: 配置管理器
        flood_events: 洪水场次列表
        
    Returns:
        运行结果
    """
    logger = logging.getLogger(__name__)
    logger.info("OI插值功能正在开发中...")
    
    # TODO: 实现OI插值运行器
    return {'method': 'OI', 'status': 'not_implemented'}

def run_prism_interpolation(config: ConfigManager, flood_events: List[str] = None) -> Dict[str, Any]:
    """
    运行PRISM插值
    
    Args:
        config: 配置管理器
        flood_events: 洪水场次列表
        
    Returns:
        运行结果
    """
    logger = logging.getLogger(__name__)
    logger.info("PRISM插值功能正在开发中...")
    
    # TODO: 实现PRISM插值运行器
    return {'method': 'PRISM', 'status': 'not_implemented'}

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='降雨空间插值系统')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--method', type=str, choices=['IDW', 'Kriging', 'OI', 'PRISM', 'all'],
                       help='插值方法')
    parser.add_argument('--flood-event', type=str, help='指定洪水场次')
    parser.add_argument('--all-events', action='store_true', help='处理所有洪水场次')
    parser.add_argument('--list-events', action='store_true', help='列出所有洪水场次')
    parser.add_argument('--test', action='store_true', help='运行系统测试')
    
    args = parser.parse_args()
    
    try:
        # 加载配置
        config = ConfigManager(args.config)
        setup_logging(config)
        
        logger = logging.getLogger(__name__)
        logger.info("降雨空间插值系统启动")
        
        # 运行测试
        if args.test:
            logger.info("运行系统测试")
            from test_system import main as test_main
            test_main()
            return
        
        # 列出洪水场次
        if args.list_events:
            processor = DataProcessor(config.get('paths.base_path'))
            flood_events = processor.get_flood_events()
            print("可用的洪水场次:")
            for i, event in enumerate(flood_events, 1):
                print(f"  {i:2d}. {event}")
            return
        
        # 确定要处理的洪水场次
        flood_events = None
        if args.all_events:
            processor = DataProcessor(config.get('paths.base_path'))
            flood_events = processor.get_flood_events()
        elif args.flood_event:
            flood_events = [args.flood_event]
        
        # 确定要运行的方法
        methods_to_run = []
        if args.method:
            if args.method == 'all':
                methods_to_run = config.get_enabled_methods()
            else:
                methods_to_run = [args.method]
        else:
            # 根据配置决定
            run_mode = config.get_run_mode()
            target_method = config.get_target_method()
            
            if 'all_methods' in run_mode:
                methods_to_run = config.get_enabled_methods()
            elif target_method:
                methods_to_run = [target_method]
            else:
                methods_to_run = ['IDW']  # 默认使用IDW
        
        # 运行插值方法
        method_runners = {
            'IDW': run_idw_interpolation,
            'Kriging': run_kriging_interpolation,
            'OI': run_oi_interpolation,
            'PRISM': run_prism_interpolation
        }
        
        all_results = []
        
        for method in methods_to_run:
            if method in method_runners:
                logger.info(f"开始运行 {method} 插值")
                
                if config.is_method_enabled(method):
                    result = method_runners[method](config, flood_events)
                    all_results.append(result)
                    
                    if result['status'] == 'completed':
                        logger.info(f"{method} 插值完成")
                    elif result['status'] == 'not_implemented':
                        logger.warning(f"{method} 插值尚未实现")
                    else:
                        logger.error(f"{method} 插值失败: {result.get('message', '未知错误')}")
                else:
                    logger.warning(f"{method} 插值未启用")
            else:
                logger.error(f"不支持的插值方法: {method}")
        
        # 输出结果摘要
        print("\n=== 运行结果摘要 ===")
        for result in all_results:
            method = result['method']
            status = result['status']
            print(f"{method}: {status}")
            
            if status == 'completed' and 'results' in result:
                completed_events = sum(1 for r in result['results'] if r.get('status') == 'completed')
                total_events = len(result['results'])
                print(f"  处理洪水场次: {completed_events}/{total_events}")
        
        logger.info("降雨空间插值系统运行完成")
        
    except Exception as e:
        print(f"系统运行出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()

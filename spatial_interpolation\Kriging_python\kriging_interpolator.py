# -*- coding: utf-8 -*-
"""
<PERSON>rig<PERSON> (克里金插值) 实现
包含普通克里金和变异函数拟合
"""

import numpy as np
from typing import List, Tuple, Dict, Any, Optional, Callable
import logging
from scipy.optimize import minimize
from scipy.spatial.distance import cdist
from scipy.linalg import solve, LinAlgError
import warnings
import gc

class KrigingInterpolator:
    """Kriging插值器"""
    
    def __init__(self,
                 variogram_model: str = 'spherical',
                 search_radius: float = 0.8,
                 min_neighbors: int = 3,
                 max_neighbors: int = 15,
                 nugget: float = 0.0,
                 sill: float = 1.0,
                 range_param: float = 0.3,
                 enable_optimization: bool = True):
        """
        初始化Kriging插值器
        
        Args:
            variogram_model: 变异函数模型类型 ('spherical', 'exponential', 'gaussian', 'linear')
            search_radius: 搜索半径（度）
            min_neighbors: 最小邻近点数
            max_neighbors: 最大邻近点数
            nugget: 块金效应
            sill: 基台值
            range_param: 变程
            enable_optimization: 是否启用参数优化
        """
        self.variogram_model = variogram_model
        self.search_radius = search_radius
        self.min_neighbors = min_neighbors
        self.max_neighbors = max_neighbors
        self.nugget = nugget
        self.sill = sill
        self.range_param = range_param
        self.enable_optimization = enable_optimization
        
        self.logger = logging.getLogger(__name__)
        
        # 变异函数字典
        self.variogram_functions = {
            'spherical': self._spherical_variogram,
            'exponential': self._exponential_variogram,
            'gaussian': self._gaussian_variogram,
            'linear': self._linear_variogram
        }
        
        # 缓存
        self._station_coords = None
        self._station_values = None
        self._fitted_params = None
    
    def fit(self, station_coords: np.ndarray, station_values: np.ndarray):
        """
        拟合Kriging插值器
        
        Args:
            station_coords: 站点坐标数组，形状为(n_stations, 2) [经度, 纬度]
            station_values: 站点观测值数组，形状为(n_stations,)
        """
        self._station_coords = np.array(station_coords)
        self._station_values = np.array(station_values)
        
        # 移除NaN值
        valid_mask = ~np.isnan(self._station_values)
        self._station_coords = self._station_coords[valid_mask]
        self._station_values = self._station_values[valid_mask]
        
        if len(self._station_values) < self.min_neighbors:
            raise ValueError(f"有效站点数量 {len(self._station_values)} 少于最小邻近点数 {self.min_neighbors}")
        
        # 拟合变异函数参数
        if self.enable_optimization:
            self._fit_variogram_parameters()
        else:
            self._fitted_params = {
                'nugget': self.nugget,
                'sill': self.sill,
                'range': self.range_param
            }
        
        self.logger.info(f"Kriging插值器已拟合，使用 {len(self._station_values)} 个有效站点")
        self.logger.info(f"变异函数参数: {self._fitted_params}")
    
    def predict(self, target_coords: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        执行Kriging插值预测
        
        Args:
            target_coords: 目标点坐标数组，形状为(n_targets, 2) [经度, 纬度]
            
        Returns:
            (插值结果数组, 插值方差数组)
        """
        if self._station_coords is None or self._station_values is None:
            raise ValueError("插值器尚未拟合，请先调用fit方法")
        
        target_coords = np.array(target_coords)
        n_targets = len(target_coords)
        
        self.logger.info(f"开始Kriging插值，目标点数: {n_targets}")
        
        predictions = np.full(n_targets, np.nan)
        variances = np.full(n_targets, np.nan)
        
        for i, target_coord in enumerate(target_coords):
            pred, var = self._interpolate_single_point(target_coord)
            predictions[i] = pred
            variances[i] = var
            
            # 每处理1000个点输出一次进度
            if (i + 1) % 1000 == 0:
                self.logger.info(f"已处理 {i + 1}/{n_targets} 个点")
        
        self.logger.info("Kriging插值完成")
        return predictions, variances
    
    def _fit_variogram_parameters(self):
        """拟合变异函数参数"""
        self.logger.info("开始拟合变异函数参数")
        
        # 计算经验变异函数
        distances, semivariances = self._calculate_empirical_variogram()
        
        if len(distances) < 3:
            self.logger.warning("数据点太少，无法拟合变异函数，使用默认参数")
            self._fitted_params = {
                'nugget': self.nugget,
                'sill': self.sill,
                'range': self.range_param
            }
            return
        
        # 设置参数边界
        max_distance = np.max(distances)
        max_semivariance = np.max(semivariances)
        
        bounds = [
            (0, max_semivariance * 0.5),  # nugget
            (max_semivariance * 0.1, max_semivariance * 2),  # sill
            (max_distance * 0.01, max_distance)  # range
        ]
        
        # 初始参数
        initial_params = [
            min(self.nugget, bounds[0][1]),
            np.clip(self.sill, bounds[1][0], bounds[1][1]),
            np.clip(self.range_param, bounds[2][0], bounds[2][1])
        ]
        
        # 目标函数
        def objective(params):
            nugget, sill, range_val = params
            predicted_semivariances = self._variogram_function(
                distances, nugget, sill, range_val
            )
            return np.sum((semivariances - predicted_semivariances) ** 2)
        
        # 优化
        try:
            result = minimize(
                objective,
                initial_params,
                bounds=bounds,
                method='L-BFGS-B'
            )
            
            if result.success:
                self._fitted_params = {
                    'nugget': result.x[0],
                    'sill': result.x[1],
                    'range': result.x[2]
                }
                self.logger.info("变异函数参数拟合成功")
            else:
                self.logger.warning("变异函数参数拟合失败，使用默认参数")
                self._fitted_params = {
                    'nugget': self.nugget,
                    'sill': self.sill,
                    'range': self.range_param
                }
                
        except Exception as e:
            self.logger.error(f"变异函数参数拟合出错: {e}")
            self._fitted_params = {
                'nugget': self.nugget,
                'sill': self.sill,
                'range': self.range_param
            }
    
    def _calculate_empirical_variogram(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算经验变异函数
        
        Returns:
            (距离数组, 半方差数组)
        """
        n_points = len(self._station_coords)
        
        # 计算所有点对之间的距离和半方差
        distances = []
        semivariances = []
        
        for i in range(n_points):
            for j in range(i + 1, n_points):
                # 计算距离
                dist = np.sqrt(
                    (self._station_coords[i, 0] - self._station_coords[j, 0]) ** 2 +
                    (self._station_coords[i, 1] - self._station_coords[j, 1]) ** 2
                )
                
                # 计算半方差
                semivar = 0.5 * (self._station_values[i] - self._station_values[j]) ** 2
                
                distances.append(dist)
                semivariances.append(semivar)
        
        distances = np.array(distances)
        semivariances = np.array(semivariances)
        
        # 按距离分组计算平均半方差
        if len(distances) > 20:
            # 如果数据点多，进行分组
            n_bins = min(15, len(distances) // 3)
            bin_edges = np.linspace(0, np.max(distances), n_bins + 1)
            
            binned_distances = []
            binned_semivariances = []
            
            for i in range(n_bins):
                mask = (distances >= bin_edges[i]) & (distances < bin_edges[i + 1])
                if np.sum(mask) > 0:
                    binned_distances.append(np.mean(distances[mask]))
                    binned_semivariances.append(np.mean(semivariances[mask]))
            
            return np.array(binned_distances), np.array(binned_semivariances)
        else:
            return distances, semivariances
    
    def _interpolate_single_point(self, target_coord: np.ndarray) -> Tuple[float, float]:
        """
        对单个点进行Kriging插值
        
        Args:
            target_coord: 目标点坐标 [经度, 纬度]
            
        Returns:
            (插值结果, 插值方差)
        """
        # 计算距离
        distances = cdist([target_coord], self._station_coords)[0]
        
        # 筛选邻近点
        valid_indices = distances <= self.search_radius
        if np.sum(valid_indices) < self.min_neighbors:
            # 如果搜索半径内点数不足，选择最近的点
            sorted_indices = np.argsort(distances)
            if len(sorted_indices) >= self.min_neighbors:
                valid_indices = sorted_indices[:self.min_neighbors]
            else:
                valid_indices = sorted_indices
        else:
            # 限制最大邻近点数
            valid_distances = distances[valid_indices]
            if len(valid_distances) > self.max_neighbors:
                sorted_valid_indices = np.argsort(valid_distances)[:self.max_neighbors]
                temp_indices = np.where(valid_indices)[0]
                valid_indices = np.zeros_like(valid_indices, dtype=bool)
                valid_indices[temp_indices[sorted_valid_indices]] = True
        
        if isinstance(valid_indices, np.ndarray) and valid_indices.dtype == bool:
            neighbor_coords = self._station_coords[valid_indices]
            neighbor_values = self._station_values[valid_indices]
        else:
            neighbor_coords = self._station_coords[valid_indices]
            neighbor_values = self._station_values[valid_indices]
        
        if len(neighbor_coords) == 0:
            return np.nan, np.nan
        
        # 构建Kriging方程组
        try:
            prediction, variance = self._solve_kriging_system(
                target_coord, neighbor_coords, neighbor_values
            )
            return prediction, variance
            
        except Exception as e:
            self.logger.warning(f"Kriging方程求解失败: {e}")
            return np.nan, np.nan

    def _solve_kriging_system(self, target_coord: np.ndarray,
                            neighbor_coords: np.ndarray,
                            neighbor_values: np.ndarray) -> Tuple[float, float]:
        """
        求解Kriging方程组

        Args:
            target_coord: 目标点坐标
            neighbor_coords: 邻近点坐标
            neighbor_values: 邻近点值

        Returns:
            (插值结果, 插值方差)
        """
        n = len(neighbor_coords)

        # 构建协方差矩阵
        C = np.zeros((n + 1, n + 1))

        # 计算邻近点之间的协方差
        for i in range(n):
            for j in range(n):
                if i == j:
                    C[i, j] = self._fitted_params['sill']
                else:
                    dist = np.sqrt(np.sum((neighbor_coords[i] - neighbor_coords[j]) ** 2))
                    C[i, j] = self._fitted_params['sill'] - self._variogram_function(
                        dist,
                        self._fitted_params['nugget'],
                        self._fitted_params['sill'],
                        self._fitted_params['range']
                    )

        # 添加拉格朗日乘数约束
        C[n, :n] = 1.0
        C[:n, n] = 1.0
        C[n, n] = 0.0

        # 构建右侧向量
        b = np.zeros(n + 1)
        for i in range(n):
            dist = np.sqrt(np.sum((target_coord - neighbor_coords[i]) ** 2))
            b[i] = self._fitted_params['sill'] - self._variogram_function(
                dist,
                self._fitted_params['nugget'],
                self._fitted_params['sill'],
                self._fitted_params['range']
            )
        b[n] = 1.0

        # 求解方程组
        try:
            weights = solve(C, b)
        except LinAlgError:
            # 如果矩阵奇异，使用最小二乘解
            weights = np.linalg.lstsq(C, b, rcond=None)[0]

        # 计算插值结果
        prediction = np.sum(weights[:n] * neighbor_values)

        # 计算插值方差
        variance = self._fitted_params['sill'] - np.sum(weights[:n] * b[:n]) - weights[n]
        variance = max(0, variance)  # 确保方差非负

        return prediction, variance

    def _variogram_function(self, distance: float, nugget: float,
                          sill: float, range_param: float) -> float:
        """
        计算变异函数值

        Args:
            distance: 距离
            nugget: 块金效应
            sill: 基台值
            range_param: 变程

        Returns:
            变异函数值
        """
        if self.variogram_model in self.variogram_functions:
            return self.variogram_functions[self.variogram_model](
                distance, nugget, sill, range_param
            )
        else:
            raise ValueError(f"不支持的变异函数模型: {self.variogram_model}")

    def _spherical_variogram(self, distance: float, nugget: float,
                           sill: float, range_param: float) -> float:
        """球状变异函数"""
        if distance == 0:
            return 0
        elif distance <= range_param:
            return nugget + (sill - nugget) * (
                1.5 * distance / range_param - 0.5 * (distance / range_param) ** 3
            )
        else:
            return sill

    def _exponential_variogram(self, distance: float, nugget: float,
                             sill: float, range_param: float) -> float:
        """指数变异函数"""
        if distance == 0:
            return 0
        else:
            return nugget + (sill - nugget) * (1 - np.exp(-3 * distance / range_param))

    def _gaussian_variogram(self, distance: float, nugget: float,
                          sill: float, range_param: float) -> float:
        """高斯变异函数"""
        if distance == 0:
            return 0
        else:
            return nugget + (sill - nugget) * (1 - np.exp(-3 * (distance / range_param) ** 2))

    def _linear_variogram(self, distance: float, nugget: float,
                        sill: float, range_param: float) -> float:
        """线性变异函数"""
        if distance == 0:
            return 0
        elif distance <= range_param:
            return nugget + (sill - nugget) * distance / range_param
        else:
            return sill

    def get_parameters(self) -> Dict[str, Any]:
        """
        获取当前参数

        Returns:
            参数字典
        """
        params = {
            'variogram_model': self.variogram_model,
            'search_radius': self.search_radius,
            'min_neighbors': self.min_neighbors,
            'max_neighbors': self.max_neighbors,
            'enable_optimization': self.enable_optimization
        }

        if self._fitted_params:
            params.update(self._fitted_params)
        else:
            params.update({
                'nugget': self.nugget,
                'sill': self.sill,
                'range': self.range_param
            })

        return params

    def set_parameters(self, **params):
        """
        设置参数

        Args:
            **params: 参数字典
        """
        for key, value in params.items():
            if hasattr(self, key):
                setattr(self, key, value)
            elif key in ['range']:
                setattr(self, 'range_param', value)

    def clear_cache(self):
        """清理缓存"""
        self._station_coords = None
        self._station_values = None
        self._fitted_params = None
        gc.collect()

# -*- coding: utf-8 -*-
"""
OI (最优插值) 实现
Optimal Interpolation
"""

import numpy as np
from typing import List, Tuple, Dict, Any, Optional
import logging
from scipy.optimize import minimize
from scipy.spatial.distance import cdist
from scipy.linalg import solve, LinAlgError
import warnings
import gc

class OIInterpolator:
    """OI最优插值器"""
    
    def __init__(self,
                 covariance_model: str = 'exponential',
                 search_radius: float = 0.8,
                 min_neighbors: int = 3,
                 max_neighbors: int = 15,
                 variance: float = 1.0,
                 correlation_length: float = 0.3,
                 observation_error_variance: float = 0.1,
                 enable_optimization: bool = True):
        """
        初始化OI插值器
        
        Args:
            covariance_model: 协方差函数模型 ('exponential', 'gaussian', 'spherical')
            search_radius: 搜索半径（度）
            min_neighbors: 最小邻近点数
            max_neighbors: 最大邻近点数
            variance: 方差
            correlation_length: 相关长度
            observation_error_variance: 观测误差方差
            enable_optimization: 是否启用参数优化
        """
        self.covariance_model = covariance_model
        self.search_radius = search_radius
        self.min_neighbors = min_neighbors
        self.max_neighbors = max_neighbors
        self.variance = variance
        self.correlation_length = correlation_length
        self.observation_error_variance = observation_error_variance
        self.enable_optimization = enable_optimization
        
        self.logger = logging.getLogger(__name__)
        
        # 协方差函数字典
        self.covariance_functions = {
            'exponential': self._exponential_covariance,
            'gaussian': self._gaussian_covariance,
            'spherical': self._spherical_covariance
        }
        
        # 缓存
        self._station_coords = None
        self._station_values = None
        self._fitted_params = None
    
    def fit(self, station_coords: np.ndarray, station_values: np.ndarray):
        """
        拟合OI插值器
        
        Args:
            station_coords: 站点坐标数组，形状为(n_stations, 2) [经度, 纬度]
            station_values: 站点观测值数组，形状为(n_stations,)
        """
        self._station_coords = np.array(station_coords)
        self._station_values = np.array(station_values)
        
        # 移除NaN值
        valid_mask = ~np.isnan(self._station_values)
        self._station_coords = self._station_coords[valid_mask]
        self._station_values = self._station_values[valid_mask]
        
        if len(self._station_values) < self.min_neighbors:
            raise ValueError(f"有效站点数量 {len(self._station_values)} 少于最小邻近点数 {self.min_neighbors}")
        
        # 拟合协方差函数参数
        if self.enable_optimization:
            self._fit_covariance_parameters()
        else:
            self._fitted_params = {
                'variance': self.variance,
                'correlation_length': self.correlation_length,
                'observation_error_variance': self.observation_error_variance
            }
        
        self.logger.info(f"OI插值器已拟合，使用 {len(self._station_values)} 个有效站点")
        self.logger.info(f"协方差函数参数: {self._fitted_params}")
    
    def predict(self, target_coords: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        执行OI插值预测
        
        Args:
            target_coords: 目标点坐标数组，形状为(n_targets, 2) [经度, 纬度]
            
        Returns:
            (插值结果数组, 插值方差数组)
        """
        if self._station_coords is None or self._station_values is None:
            raise ValueError("插值器尚未拟合，请先调用fit方法")
        
        target_coords = np.array(target_coords)
        n_targets = len(target_coords)
        
        self.logger.info(f"开始OI插值，目标点数: {n_targets}")
        
        predictions = np.full(n_targets, np.nan)
        variances = np.full(n_targets, np.nan)
        
        for i, target_coord in enumerate(target_coords):
            pred, var = self._interpolate_single_point(target_coord)
            predictions[i] = pred
            variances[i] = var
            
            # 每处理1000个点输出一次进度
            if (i + 1) % 1000 == 0:
                self.logger.info(f"已处理 {i + 1}/{n_targets} 个点")
        
        self.logger.info("OI插值完成")
        return predictions, variances
    
    def _fit_covariance_parameters(self):
        """拟合协方差函数参数"""
        self.logger.info("开始拟合协方差函数参数")
        
        # 计算经验协方差
        distances, covariances = self._calculate_empirical_covariance()
        
        if len(distances) < 3:
            self.logger.warning("数据点太少，无法拟合协方差函数，使用默认参数")
            self._fitted_params = {
                'variance': self.variance,
                'correlation_length': self.correlation_length,
                'observation_error_variance': self.observation_error_variance
            }
            return
        
        # 设置参数边界
        max_distance = np.max(distances)
        max_covariance = np.max(covariances)
        
        bounds = [
            (max_covariance * 0.1, max_covariance * 2),  # variance
            (max_distance * 0.01, max_distance),  # correlation_length
            (0, max_covariance * 0.5)  # observation_error_variance
        ]
        
        # 初始参数
        initial_params = [
            np.clip(self.variance, bounds[0][0], bounds[0][1]),
            np.clip(self.correlation_length, bounds[1][0], bounds[1][1]),
            np.clip(self.observation_error_variance, bounds[2][0], bounds[2][1])
        ]
        
        # 目标函数
        def objective(params):
            variance, correlation_length, obs_error_var = params
            predicted_covariances = self._covariance_function(
                distances, variance, correlation_length
            )
            return np.sum((covariances - predicted_covariances) ** 2)
        
        # 优化
        try:
            result = minimize(
                objective,
                initial_params,
                bounds=bounds,
                method='L-BFGS-B'
            )
            
            if result.success:
                self._fitted_params = {
                    'variance': result.x[0],
                    'correlation_length': result.x[1],
                    'observation_error_variance': result.x[2]
                }
                self.logger.info("协方差函数参数拟合成功")
            else:
                self.logger.warning("协方差函数参数拟合失败，使用默认参数")
                self._fitted_params = {
                    'variance': self.variance,
                    'correlation_length': self.correlation_length,
                    'observation_error_variance': self.observation_error_variance
                }
                
        except Exception as e:
            self.logger.error(f"协方差函数参数拟合出错: {e}")
            self._fitted_params = {
                'variance': self.variance,
                'correlation_length': self.correlation_length,
                'observation_error_variance': self.observation_error_variance
            }
    
    def _calculate_empirical_covariance(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算经验协方差函数
        
        Returns:
            (距离数组, 协方差数组)
        """
        n_points = len(self._station_coords)
        
        # 计算所有点对之间的距离和协方差
        distances = []
        covariances = []
        
        # 计算数据的方差
        data_variance = np.var(self._station_values)
        
        for i in range(n_points):
            for j in range(i + 1, n_points):
                # 计算距离
                dist = np.sqrt(
                    (self._station_coords[i, 0] - self._station_coords[j, 0]) ** 2 +
                    (self._station_coords[i, 1] - self._station_coords[j, 1]) ** 2
                )
                
                # 计算协方差
                cov = (self._station_values[i] - np.mean(self._station_values)) * \
                      (self._station_values[j] - np.mean(self._station_values))
                
                distances.append(dist)
                covariances.append(cov)
        
        distances = np.array(distances)
        covariances = np.array(covariances)
        
        # 按距离分组计算平均协方差
        if len(distances) > 20:
            # 如果数据点多，进行分组
            n_bins = min(15, len(distances) // 3)
            bin_edges = np.linspace(0, np.max(distances), n_bins + 1)
            
            binned_distances = []
            binned_covariances = []
            
            for i in range(n_bins):
                mask = (distances >= bin_edges[i]) & (distances < bin_edges[i + 1])
                if np.sum(mask) > 0:
                    binned_distances.append(np.mean(distances[mask]))
                    binned_covariances.append(np.mean(covariances[mask]))
            
            return np.array(binned_distances), np.array(binned_covariances)
        else:
            return distances, covariances
    
    def _interpolate_single_point(self, target_coord: np.ndarray) -> Tuple[float, float]:
        """
        对单个点进行OI插值
        
        Args:
            target_coord: 目标点坐标 [经度, 纬度]
            
        Returns:
            (插值结果, 插值方差)
        """
        # 计算距离
        distances = cdist([target_coord], self._station_coords)[0]
        
        # 筛选邻近点
        valid_indices = distances <= self.search_radius
        if np.sum(valid_indices) < self.min_neighbors:
            # 如果搜索半径内点数不足，选择最近的点
            sorted_indices = np.argsort(distances)
            if len(sorted_indices) >= self.min_neighbors:
                valid_indices = sorted_indices[:self.min_neighbors]
            else:
                valid_indices = sorted_indices
        else:
            # 限制最大邻近点数
            valid_distances = distances[valid_indices]
            if len(valid_distances) > self.max_neighbors:
                sorted_valid_indices = np.argsort(valid_distances)[:self.max_neighbors]
                temp_indices = np.where(valid_indices)[0]
                valid_indices = np.zeros_like(valid_indices, dtype=bool)
                valid_indices[temp_indices[sorted_valid_indices]] = True
        
        if isinstance(valid_indices, np.ndarray) and valid_indices.dtype == bool:
            neighbor_coords = self._station_coords[valid_indices]
            neighbor_values = self._station_values[valid_indices]
        else:
            neighbor_coords = self._station_coords[valid_indices]
            neighbor_values = self._station_values[valid_indices]
        
        if len(neighbor_coords) == 0:
            return np.nan, np.nan
        
        # 构建OI方程组
        try:
            prediction, variance = self._solve_oi_system(
                target_coord, neighbor_coords, neighbor_values
            )
            return prediction, variance
            
        except Exception as e:
            self.logger.warning(f"OI方程求解失败: {e}")
            return np.nan, np.nan

    def _solve_oi_system(self, target_coord: np.ndarray,
                        neighbor_coords: np.ndarray,
                        neighbor_values: np.ndarray) -> Tuple[float, float]:
        """
        求解OI方程组

        Args:
            target_coord: 目标点坐标
            neighbor_coords: 邻近点坐标
            neighbor_values: 邻近点值

        Returns:
            (插值结果, 插值方差)
        """
        n = len(neighbor_coords)

        # 构建观测协方差矩阵 R
        R = np.zeros((n, n))

        for i in range(n):
            for j in range(n):
                if i == j:
                    # 对角线元素包含观测误差
                    R[i, j] = self._fitted_params['observation_error_variance']
                else:
                    dist = np.sqrt(np.sum((neighbor_coords[i] - neighbor_coords[j]) ** 2))
                    R[i, j] = self._covariance_function(
                        dist,
                        self._fitted_params['variance'],
                        self._fitted_params['correlation_length']
                    )

        # 构建背景与观测的协方差向量 H
        H = np.zeros(n)
        for i in range(n):
            dist = np.sqrt(np.sum((target_coord - neighbor_coords[i]) ** 2))
            H[i] = self._covariance_function(
                dist,
                self._fitted_params['variance'],
                self._fitted_params['correlation_length']
            )

        # 计算权重 W = H^T * R^(-1)
        try:
            R_inv = np.linalg.inv(R)
            weights = H.T @ R_inv
        except LinAlgError:
            # 如果矩阵奇异，使用伪逆
            R_inv = np.linalg.pinv(R)
            weights = H.T @ R_inv

        # 计算插值结果
        # 假设背景场为观测值的平均值
        background = np.mean(neighbor_values)
        innovations = neighbor_values - background

        prediction = background + weights @ innovations

        # 计算插值方差
        variance = self._fitted_params['variance'] - weights @ H
        variance = max(0, variance)  # 确保方差非负

        return prediction, variance

    def _covariance_function(self, distance: float, variance: float,
                           correlation_length: float) -> float:
        """
        计算协方差函数值

        Args:
            distance: 距离
            variance: 方差
            correlation_length: 相关长度

        Returns:
            协方差值
        """
        if self.covariance_model in self.covariance_functions:
            return self.covariance_functions[self.covariance_model](
                distance, variance, correlation_length
            )
        else:
            raise ValueError(f"不支持的协方差函数模型: {self.covariance_model}")

    def _exponential_covariance(self, distance: float, variance: float,
                              correlation_length: float) -> float:
        """指数协方差函数"""
        if distance == 0:
            return variance
        else:
            return variance * np.exp(-distance / correlation_length)

    def _gaussian_covariance(self, distance: float, variance: float,
                           correlation_length: float) -> float:
        """高斯协方差函数"""
        if distance == 0:
            return variance
        else:
            return variance * np.exp(-(distance / correlation_length) ** 2)

    def _spherical_covariance(self, distance: float, variance: float,
                            correlation_length: float) -> float:
        """球状协方差函数"""
        if distance == 0:
            return variance
        elif distance <= correlation_length:
            ratio = distance / correlation_length
            return variance * (1 - 1.5 * ratio + 0.5 * ratio ** 3)
        else:
            return 0

    def get_parameters(self) -> Dict[str, Any]:
        """
        获取当前参数

        Returns:
            参数字典
        """
        params = {
            'covariance_model': self.covariance_model,
            'search_radius': self.search_radius,
            'min_neighbors': self.min_neighbors,
            'max_neighbors': self.max_neighbors,
            'enable_optimization': self.enable_optimization
        }

        if self._fitted_params:
            params.update(self._fitted_params)
        else:
            params.update({
                'variance': self.variance,
                'correlation_length': self.correlation_length,
                'observation_error_variance': self.observation_error_variance
            })

        return params

    def set_parameters(self, **params):
        """
        设置参数

        Args:
            **params: 参数字典
        """
        for key, value in params.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def clear_cache(self):
        """清理缓存"""
        self._station_coords = None
        self._station_values = None
        self._fitted_params = None
        gc.collect()

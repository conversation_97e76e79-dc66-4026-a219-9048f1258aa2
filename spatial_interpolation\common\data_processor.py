# -*- coding: utf-8 -*-
"""
数据处理模块
负责读取站点数据、雨量数据、地形数据等
"""

import os
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime
import gc

class DataProcessor:
    """数据处理器"""
    
    def __init__(self, base_path: str):
        """
        初始化数据处理器
        
        Args:
            base_path: 项目根目录路径
        """
        self.base_path = base_path
        self.logger = logging.getLogger(__name__)
        
        # 数据路径
        self.stations_file = os.path.join(base_path, 'stations.csv')
        self.delaunay_file = os.path.join(base_path, 'Delaunay_Molan.csv')
        self.input_dir = os.path.join(base_path, 'input_another')
        self.terrain_dir = os.path.join(base_path, 'terrain', '90')
        
        # 缓存
        self._stations_cache = None
        self._delaunay_cache = None
        self._terrain_cache = {}
        
    def load_stations(self) -> pd.DataFrame:
        """
        加载站点信息
        
        Returns:
            站点信息DataFrame，包含站点编号、经度、纬度、名称
        """
        if self._stations_cache is None:
            try:
                self._stations_cache = pd.read_csv(
                    self.stations_file, 
                    encoding='utf-8'
                )
                self.logger.info(f"成功加载 {len(self._stations_cache)} 个站点信息")
            except Exception as e:
                self.logger.error(f"加载站点信息失败: {e}")
                raise
                
        return self._stations_cache.copy()
    
    def load_delaunay_molan(self) -> pd.DataFrame:
        """
        加载Delaunay三角网和莫兰指数分析结果
        
        Returns:
            分析结果DataFrame
        """
        if self._delaunay_cache is None:
            try:
                self._delaunay_cache = pd.read_csv(
                    self.delaunay_file,
                    encoding='utf-8'
                )
                self.logger.info(f"成功加载 {len(self._delaunay_cache)} 条Delaunay-Molan分析结果")
            except Exception as e:
                self.logger.error(f"加载Delaunay-Molan分析结果失败: {e}")
                raise
                
        return self._delaunay_cache.copy()
    
    def get_flood_events(self) -> List[str]:
        """
        获取所有洪水场次列表
        
        Returns:
            洪水场次名称列表
        """
        flood_events = []
        if os.path.exists(self.input_dir):
            for item in os.listdir(self.input_dir):
                item_path = os.path.join(self.input_dir, item)
                if os.path.isdir(item_path):
                    flood_events.append(item)
        
        flood_events.sort()
        self.logger.info(f"发现 {len(flood_events)} 个洪水场次: {flood_events}")
        return flood_events
    
    def load_rainfall_data(self, flood_event: str) -> Dict[str, pd.DataFrame]:
        """
        加载指定洪水场次的雨量数据
        
        Args:
            flood_event: 洪水场次名称
            
        Returns:
            字典，键为站点编号，值为雨量数据DataFrame
        """
        flood_dir = os.path.join(self.input_dir, flood_event)
        if not os.path.exists(flood_dir):
            raise FileNotFoundError(f"洪水场次目录不存在: {flood_dir}")
        
        rainfall_data = {}
        csv_files = [f for f in os.listdir(flood_dir) if f.endswith('.csv')]
        
        self.logger.info(f"开始加载洪水场次 {flood_event} 的雨量数据，共 {len(csv_files)} 个文件")
        
        for csv_file in csv_files:
            station_id = os.path.splitext(csv_file)[0]
            file_path = os.path.join(flood_dir, csv_file)
            
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
                # 转换时间列
                df['时间'] = pd.to_datetime(df['时间'])
                # 确保雨量为数值类型
                df['雨量'] = pd.to_numeric(df['雨量'], errors='coerce')
                df['雨量'] = df['雨量'].fillna(0.0)
                
                rainfall_data[station_id] = df
                
            except Exception as e:
                self.logger.warning(f"加载站点 {station_id} 数据失败: {e}")
                continue
        
        self.logger.info(f"成功加载 {len(rainfall_data)} 个站点的雨量数据")
        return rainfall_data
    
    def get_interpolation_info(self, flood_event: str) -> pd.DataFrame:
        """
        获取指定洪水场次的插值信息
        
        Args:
            flood_event: 洪水场次名称
            
        Returns:
            插值信息DataFrame
        """
        delaunay_data = self.load_delaunay_molan()
        flood_data = delaunay_data[delaunay_data['flood_event'] == flood_event].copy()
        
        if flood_data.empty:
            self.logger.warning(f"未找到洪水场次 {flood_event} 的插值信息")
            return pd.DataFrame()
        
        self.logger.info(f"洪水场次 {flood_event} 有 {len(flood_data)} 个待插值站点")
        return flood_data
    
    def parse_interp_stations(self, interp_stations_info: str) -> List[Tuple[str, str]]:
        """
        解析插值站点信息字符串
        
        Args:
            interp_stations_info: 插值站点信息字符串，格式如"茶山(80607800);蒙山(80608500)"
            
        Returns:
            插值站点列表，每个元素为(站点名称, 站点编号)的元组
        """
        stations = []
        if pd.isna(interp_stations_info) or not interp_stations_info:
            return stations
        
        station_parts = interp_stations_info.split(';')
        for part in station_parts:
            part = part.strip()
            if '(' in part and ')' in part:
                name = part.split('(')[0]
                station_id = part.split('(')[1].split(')')[0]
                stations.append((name, station_id))
        
        return stations
    
    def filter_zero_rainfall_times(self, rainfall_data: Dict[str, pd.DataFrame], 
                                 min_nonzero_stations: int = 3) -> List[datetime]:
        """
        过滤掉大部分站点雨量为0的时刻
        
        Args:
            rainfall_data: 雨量数据字典
            min_nonzero_stations: 最少非零雨量站点数
            
        Returns:
            有效时刻列表
        """
        if not rainfall_data:
            return []
        
        # 获取所有时刻
        all_times = set()
        for df in rainfall_data.values():
            all_times.update(df['时间'].tolist())
        
        all_times = sorted(list(all_times))
        valid_times = []
        
        for time_point in all_times:
            nonzero_count = 0
            for df in rainfall_data.values():
                time_data = df[df['时间'] == time_point]
                if not time_data.empty and time_data['雨量'].iloc[0] > 0:
                    nonzero_count += 1
            
            if nonzero_count >= min_nonzero_stations:
                valid_times.append(time_point)
        
        self.logger.info(f"从 {len(all_times)} 个时刻中筛选出 {len(valid_times)} 个有效时刻")
        return valid_times
    
    def get_station_coordinates(self, station_id: str) -> Optional[Tuple[float, float]]:
        """
        获取站点坐标
        
        Args:
            station_id: 站点编号
            
        Returns:
            (经度, 纬度) 或 None
        """
        stations = self.load_stations()
        station_data = stations[stations['站点'] == station_id]
        
        if station_data.empty:
            return None
        
        return (station_data['经度'].iloc[0], station_data['纬度'].iloc[0])
    
    def clear_cache(self):
        """清理缓存，释放内存"""
        self._stations_cache = None
        self._delaunay_cache = None
        self._terrain_cache.clear()
        gc.collect()
        self.logger.info("已清理数据缓存")

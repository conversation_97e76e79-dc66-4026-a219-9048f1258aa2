# 降雨空间插值系统使用说明

## 系统概述

本系统实现了四种主流的降雨空间插值方法：
- **IDW (反距离权重插值)** - 已实现 ✓
- **Kriging (克里金插值)** - 开发中 🚧
- **OI (最优插值)** - 开发中 🚧  
- **PRISM (参数回归独立斜率模型)** - 开发中 🚧

系统支持参数优化、批量处理、并行计算等功能。

## 目录结构

```
spatial_interpolation/
├── common/                 # 公共模块
│   ├── data_processor.py   # 数据处理
│   ├── raster_utils.py     # 栅格工具
│   ├── evaluation_metrics.py # 评价指标
│   └── sce_ua_optimizer.py # SCE-UA优化算法
├── config/                 # 配置文件
│   ├── config.yaml         # 主配置文件
│   └── config_manager.py   # 配置管理器
├── IDW_python/             # IDW插值模块
├── Kriging_python/         # Kriging插值模块
├── OI_python/              # OI插值模块
├── PRISM_python/           # PRISM插值模块
├── docs/                   # 文档
├── output/                 # 输出目录
├── main.py                 # 主程序
└── test_system.py          # 系统测试
```

## 快速开始

### 1. 系统测试

首先运行系统测试确保环境正常：

```bash
cd spatial_interpolation
python test_system.py
```

或者使用主程序：

```bash
python main.py --test
```

### 2. 查看可用洪水场次

```bash
python main.py --list-events
```

### 3. 运行单个方法处理单个洪水场次

```bash
# 使用IDW方法处理2009-1洪水场次
python main.py --method IDW --flood-event 2009-1
```

### 4. 运行单个方法处理所有洪水场次

```bash
# 使用IDW方法处理所有洪水场次
python main.py --method IDW --all-events
```

### 5. 运行所有方法处理单个洪水场次

```bash
# 使用所有方法处理2009-1洪水场次
python main.py --method all --flood-event 2009-1
```

### 6. 运行所有方法处理所有洪水场次

```bash
# 使用所有方法处理所有洪水场次
python main.py --method all --all-events
```

## 配置文件说明

主配置文件位于 `config/config.yaml`，包含以下主要部分：

### 基本路径配置
```yaml
paths:
  base_path: "D:/pythondata/jiangyuchazhi"  # 项目根目录
  input_dir: "input_another"                # 输入数据目录
  stations_file: "stations.csv"             # 站点信息文件
  delaunay_file: "Delaunay_Molan.csv"       # Delaunay分析结果
  terrain_dir: "terrain/90"                 # 地形数据目录
  output_dir: "output"                      # 输出目录
```

### 运行模式配置
```yaml
run_mode:
  mode: "single_method_single_event"        # 运行模式
  target_flood_event: "2009-1"              # 目标洪水场次
  target_method: "IDW"                      # 目标方法
```

运行模式选项：
- `single_method_single_event`: 单一方法处理单一洪水场次
- `single_method_all_events`: 单一方法处理所有洪水场次
- `all_methods_single_event`: 所有方法处理单一洪水场次
- `all_methods_all_events`: 所有方法处理所有洪水场次

### 插值方法配置

#### IDW配置
```yaml
IDW:
  enabled: true                # 是否启用
  power: 2.0                   # 距离权重指数
  search_radius: 0.5           # 搜索半径（度）
  min_neighbors: 3             # 最小邻近点数
  max_neighbors: 12            # 最大邻近点数
  use_anisotropy: false        # 是否使用各向异性
```

#### Kriging配置
```yaml
Kriging:
  enabled: true                # 是否启用
  variogram_model: "spherical" # 变异函数类型
  enable_optimization: true    # 是否启用参数优化
  search_radius: 0.8           # 搜索半径
  min_neighbors: 3             # 最小邻近点数
  max_neighbors: 15            # 最大邻近点数
```

### SCE-UA参数优化配置
```yaml
optimization:
  enable_sce_ua: true          # 是否启用SCE-UA优化
  max_iterations: 100          # 最大迭代次数
  max_function_evaluations: 2000 # 最大函数评估次数
  max_time_seconds: 300        # 最大优化时间（秒）
```

### 并行计算配置
```yaml
parallel:
  enable_parallel: true        # 是否启用并行计算
  num_processes: 0             # 并行进程数（0表示自动检测）
  chunk_size: 1000             # 分块大小
```

## 输出结果

### 文件结构
```
output/
├── IDW/                     # IDW方法结果
│   ├── 2009-1/              # 洪水场次目录
│   │   ├── 站点名_站点编号_IDW_results.csv  # 站点插值结果
│   │   └── ...
│   ├── IDW_summary_results.csv  # 汇总结果
│   └── IDW_statistics.csv       # 统计信息
├── Kriging/                 # Kriging方法结果
├── OI/                      # OI方法结果
└── PRISM/                   # PRISM方法结果
```

### 结果文件内容

#### 站点插值结果文件
包含每个时刻的观测值和插值值：
```csv
时间,观测值,插值值
2009-04-16 03:00,0.0,0.0
2009-04-16 04:00,0.0,0.0
...
```

#### 汇总结果文件
包含所有站点的评价指标：
```csv
flood_event,station_id,station_name,longitude,latitude,valid_points,MAE,RMSE,R2,NSE,Bias,Relative_Bias,Correlation
2009-1,80629000,壬山,110.4875,24.267222,100,1.23,2.45,0.85,0.82,-0.15,-2.3,0.92
...
```

#### 统计信息文件
包含各指标的统计信息：
```csv
metric,count,mean,std,min,max,median
MAE,150,1.45,0.67,0.23,4.56,1.32
RMSE,150,2.34,1.23,0.45,7.89,2.12
...
```

## 评价指标说明

系统计算以下评价指标：

- **MAE (平均绝对误差)**: 预测值与观测值绝对误差的平均值
- **RMSE (均方根误差)**: 预测值与观测值误差平方的均方根
- **R² (决定系数)**: 预测值对观测值的解释程度
- **NSE (纳什效率系数)**: 模型效率指标，1为完美，0为平均值水平
- **Bias (偏差)**: 预测值与观测值的平均偏差
- **Relative_Bias (相对偏差)**: 相对于观测值平均值的偏差百分比
- **Correlation (相关系数)**: 预测值与观测值的线性相关程度

## 常见问题

### Q1: 如何修改插值参数？
A: 编辑 `config/config.yaml` 文件中对应方法的参数设置。

### Q2: 如何启用/禁用某个插值方法？
A: 在配置文件中设置对应方法的 `enabled` 参数为 `true` 或 `false`。

### Q3: 如何启用参数优化？
A: 设置 `optimization.enable_sce_ua: true` 和对应方法的 `enable_optimization: true`。

### Q4: 系统运行很慢怎么办？
A: 可以启用并行计算 (`parallel.enable_parallel: true`) 或减少处理的时刻数量。

### Q5: 内存不足怎么办？
A: 减少 `parallel.chunk_size` 或增加 `data_processing.memory_cleanup_interval` 的频率。

## 技术支持

如有问题，请检查：
1. 数据文件是否完整
2. 配置文件路径是否正确
3. Python环境是否安装了所需依赖
4. 查看日志文件获取详细错误信息

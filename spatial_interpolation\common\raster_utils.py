# -*- coding: utf-8 -*-
"""
栅格工具模块
负责栅格数据的读取、写入、处理等操作
"""

import os
import numpy as np
from typing import Tuple, Optional, Dict, Any
import logging
import gc

class RasterUtils:
    """栅格工具类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def read_asc_file(self, file_path: str) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        读取ASC格式栅格文件
        
        Args:
            file_path: ASC文件路径
            
        Returns:
            (数据数组, 头信息字典)
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        header = {}
        data_lines = []
        
        with open(file_path, 'r') as f:
            # 读取头信息
            for i in range(6):  # ASC文件前6行是头信息
                line = f.readline().strip()
                if line:
                    parts = line.split()
                    if len(parts) >= 2:
                        key = parts[0].lower()
                        value = parts[1]
                        
                        if key in ['ncols', 'nrows']:
                            header[key] = int(value)
                        elif key in ['xllcorner', 'yllcorner', 'cellsize']:
                            header[key] = float(value)
                        elif key == 'nodata_value':
                            header[key] = float(value)
            
            # 读取数据
            for line in f:
                line = line.strip()
                if line:
                    row_data = [float(x) for x in line.split()]
                    data_lines.append(row_data)
        
        # 转换为numpy数组
        data = np.array(data_lines, dtype=np.float32)
        
        # 将NODATA值转换为NaN
        if 'nodata_value' in header:
            data[data == header['nodata_value']] = np.nan
        
        self.logger.info(f"成功读取ASC文件: {file_path}, 尺寸: {data.shape}")
        return data, header
    
    def write_asc_file(self, file_path: str, data: np.ndarray, header: Dict[str, Any]):
        """
        写入ASC格式栅格文件
        
        Args:
            file_path: 输出文件路径
            data: 数据数组
            header: 头信息字典
        """
        # 确保输出目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 处理NaN值
        output_data = data.copy()
        nodata_value = header.get('nodata_value', -9999)
        output_data[np.isnan(output_data)] = nodata_value
        
        with open(file_path, 'w') as f:
            # 写入头信息
            f.write(f"ncols         {header['ncols']}\n")
            f.write(f"nrows         {header['nrows']}\n")
            f.write(f"xllcorner     {header['xllcorner']}\n")
            f.write(f"yllcorner     {header['yllcorner']}\n")
            f.write(f"cellsize      {header['cellsize']}\n")
            f.write(f"NODATA_value  {nodata_value}\n")
            
            # 写入数据
            for row in output_data:
                row_str = ' '.join([f"{val:.6f}" if not np.isnan(val) else f"{nodata_value}" 
                                  for val in row])
                f.write(row_str + '\n')
        
        self.logger.info(f"成功写入ASC文件: {file_path}")
    
    def get_coordinates_from_header(self, header: Dict[str, Any]) -> Tuple[np.ndarray, np.ndarray]:
        """
        根据栅格头信息生成坐标数组
        
        Args:
            header: 栅格头信息
            
        Returns:
            (经度数组, 纬度数组)
        """
        ncols = header['ncols']
        nrows = header['nrows']
        xllcorner = header['xllcorner']
        yllcorner = header['yllcorner']
        cellsize = header['cellsize']
        
        # 生成经度数组
        x = np.linspace(
            xllcorner + cellsize/2,
            xllcorner + (ncols - 0.5) * cellsize,
            ncols
        )
        
        # 生成纬度数组（注意ASC文件中数据是从北到南排列的）
        y = np.linspace(
            yllcorner + (nrows - 0.5) * cellsize,
            yllcorner + cellsize/2,
            nrows
        )
        
        # 创建网格
        X, Y = np.meshgrid(x, y)
        
        return X, Y
    
    def create_mask_from_template(self, template_data: np.ndarray) -> np.ndarray:
        """
        从模板数据创建掩膜
        
        Args:
            template_data: 模板数据数组
            
        Returns:
            掩膜数组（True表示有效区域）
        """
        return ~np.isnan(template_data)
    
    def apply_mask(self, data: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """
        应用掩膜到数据
        
        Args:
            data: 原始数据
            mask: 掩膜数组
            
        Returns:
            应用掩膜后的数据
        """
        result = data.copy()
        result[~mask] = np.nan
        return result
    
    def calculate_basin_average(self, data: np.ndarray, mask: np.ndarray) -> float:
        """
        计算流域平均值
        
        Args:
            data: 数据数组
            mask: 流域掩膜
            
        Returns:
            流域平均值
        """
        valid_data = data[mask & ~np.isnan(data)]
        if len(valid_data) == 0:
            return np.nan
        return np.mean(valid_data)
    
    def safe_filename(self, filename: str) -> str:
        """
        生成安全的文件名，替换特殊字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            安全的文件名
        """
        # 替换Windows文件名中的非法字符
        invalid_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
        safe_name = filename
        
        for char in invalid_chars:
            safe_name = safe_name.replace(char, '_')
        
        # 替换空格
        safe_name = safe_name.replace(' ', '_')
        
        return safe_name
    
    def clear_memory(self):
        """清理内存"""
        gc.collect()
        self.logger.debug("已执行内存清理")
    
    def get_raster_extent(self, header: Dict[str, Any]) -> Tuple[float, float, float, float]:
        """
        获取栅格范围
        
        Args:
            header: 栅格头信息
            
        Returns:
            (xmin, ymin, xmax, ymax)
        """
        xmin = header['xllcorner']
        ymin = header['yllcorner']
        xmax = xmin + header['ncols'] * header['cellsize']
        ymax = ymin + header['nrows'] * header['cellsize']
        
        return xmin, ymin, xmax, ymax
    
    def point_to_pixel(self, x: float, y: float, header: Dict[str, Any]) -> Tuple[int, int]:
        """
        将地理坐标转换为像素坐标
        
        Args:
            x: 经度
            y: 纬度
            header: 栅格头信息
            
        Returns:
            (行号, 列号)
        """
        col = int((x - header['xllcorner']) / header['cellsize'])
        row = int((header['yllcorner'] + header['nrows'] * header['cellsize'] - y) / header['cellsize'])
        
        # 确保在有效范围内
        col = max(0, min(col, header['ncols'] - 1))
        row = max(0, min(row, header['nrows'] - 1))
        
        return row, col

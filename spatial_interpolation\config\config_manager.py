# -*- coding: utf-8 -*-
"""
配置管理模块
负责读取和管理配置文件
"""

import os
import yaml
from typing import Dict, Any, Optional
import logging
from pathlib import Path

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认路径
        """
        self.logger = logging.getLogger(__name__)
        
        # 确定配置文件路径
        if config_file is None:
            # 获取当前文件所在目录
            current_dir = Path(__file__).parent
            config_file = current_dir / "config.yaml"
        
        self.config_file = Path(config_file)
        self.config = self._load_config()
        
        # 设置日志
        self._setup_logging()
    
    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        if not self.config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 处理路径配置
            config = self._process_paths(config)
            
            return config
            
        except Exception as e:
            raise RuntimeError(f"加载配置文件失败: {e}")
    
    def _process_paths(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理路径配置，确保路径正确
        
        Args:
            config: 原始配置字典
            
        Returns:
            处理后的配置字典
        """
        # 获取基础路径
        base_path = config.get('paths', {}).get('base_path', '')
        
        if not base_path:
            # 如果没有指定基础路径，使用配置文件所在目录的上级目录
            base_path = self.config_file.parent.parent.parent
        
        base_path = Path(base_path).resolve()
        
        # 更新路径配置
        paths = config.setdefault('paths', {})
        paths['base_path'] = str(base_path)
        
        # 处理相对路径
        relative_paths = ['input_dir', 'terrain_dir', 'output_dir']
        for path_key in relative_paths:
            if path_key in paths:
                if not os.path.isabs(paths[path_key]):
                    paths[path_key] = str(base_path / paths[path_key])
        
        # 处理文件路径
        file_paths = ['stations_file', 'delaunay_file']
        for file_key in file_paths:
            if file_key in paths:
                if not os.path.isabs(paths[file_key]):
                    paths[file_key] = str(base_path / paths[file_key])
        
        return config
    
    def _setup_logging(self):
        """设置日志配置"""
        log_config = self.config.get('logging', {})
        
        # 设置日志级别
        level = log_config.get('level', 'INFO')
        numeric_level = getattr(logging, level.upper(), logging.INFO)
        
        # 创建根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(numeric_level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 日志格式
        formatter = logging.Formatter(
            log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        
        # 控制台处理器
        if log_config.get('console', True):
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
        
        # 文件处理器
        if log_config.get('file', True):
            log_file = log_config.get('log_file', 'spatial_interpolation/logs/interpolation.log')
            
            # 确保日志目录存在
            log_path = Path(log_file)
            if not log_path.is_absolute():
                log_path = Path(self.config['paths']['base_path']) / log_file
            
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_path, encoding='utf-8')
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的嵌套键
        
        Args:
            key: 配置键，支持"section.subsection.key"格式
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 导航到最后一级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
    
    def get_method_config(self, method_name: str) -> Dict[str, Any]:
        """
        获取指定插值方法的配置
        
        Args:
            method_name: 方法名称
            
        Returns:
            方法配置字典
        """
        return self.get(f'methods.{method_name}', {})
    
    def is_method_enabled(self, method_name: str) -> bool:
        """
        检查指定方法是否启用
        
        Args:
            method_name: 方法名称
            
        Returns:
            是否启用
        """
        return self.get(f'methods.{method_name}.enabled', False)
    
    def get_enabled_methods(self) -> list:
        """
        获取所有启用的方法列表
        
        Returns:
            启用的方法名称列表
        """
        methods = self.get('methods', {})
        enabled_methods = []
        
        for method_name, method_config in methods.items():
            if method_config.get('enabled', False):
                enabled_methods.append(method_name)
        
        return enabled_methods
    
    def get_run_mode(self) -> str:
        """
        获取运行模式
        
        Returns:
            运行模式字符串
        """
        return self.get('run_mode.mode', 'single_method_single_event')
    
    def get_target_flood_event(self) -> Optional[str]:
        """
        获取目标洪水场次
        
        Returns:
            洪水场次名称或None
        """
        return self.get('run_mode.target_flood_event')
    
    def get_target_method(self) -> Optional[str]:
        """
        获取目标方法
        
        Returns:
            方法名称或None
        """
        return self.get('run_mode.target_method')
    
    def is_optimization_enabled(self, method_name: str = None) -> bool:
        """
        检查是否启用参数优化
        
        Args:
            method_name: 方法名称，如果为None则检查全局设置
            
        Returns:
            是否启用优化
        """
        if method_name:
            # 检查方法特定的优化设置
            method_optimization = self.get(f'methods.{method_name}.enable_optimization', True)
            if not method_optimization:
                return False
        
        # 检查全局优化设置
        return self.get('optimization.enable_sce_ua', True)
    
    def get_paths(self) -> Dict[str, str]:
        """
        获取所有路径配置
        
        Returns:
            路径配置字典
        """
        return self.get('paths', {})
    
    def save_config(self, output_file: Optional[str] = None):
        """
        保存配置到文件
        
        Args:
            output_file: 输出文件路径，如果为None则覆盖原文件
        """
        if output_file is None:
            output_file = self.config_file
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            self.logger.info(f"配置已保存到: {output_file}")
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            raise

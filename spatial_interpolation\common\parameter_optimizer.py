# -*- coding: utf-8 -*-
"""
参数优化模块
集成SCE-UA算法为各种插值方法优化参数
"""

import numpy as np
from typing import Dict, Any, List, Tuple, Callable, Optional
import logging
from .sce_ua_optimizer import SCEUAOptimizer
from .evaluation_metrics import EvaluationMetrics

class ParameterOptimizer:
    """参数优化器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化参数优化器
        
        Args:
            config: 优化配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.evaluator = EvaluationMetrics()
    
    def optimize_kriging_parameters(self, interpolator, station_coords: np.ndarray, 
                                  station_values: np.ndarray) -> Dict[str, Any]:
        """
        优化Kriging插值器参数
        
        Args:
            interpolator: Kriging插值器实例
            station_coords: 站点坐标
            station_values: 站点值
            
        Returns:
            优化结果
        """
        self.logger.info("开始优化Kriging参数")
        
        # 定义参数边界
        parameter_bounds = [
            (0.0, np.var(station_values)),  # nugget
            (np.var(station_values) * 0.1, np.var(station_values) * 2),  # sill
            (0.01, 2.0)  # range
        ]
        
        # 定义目标函数
        def objective_function(params):
            nugget, sill, range_param = params
            
            # 设置参数
            interpolator.nugget = nugget
            interpolator.sill = sill
            interpolator.range_param = range_param
            
            # 交叉验证
            return self._cross_validate_interpolator(
                interpolator, station_coords, station_values
            )
        
        # 创建SCE-UA优化器
        optimizer = SCEUAOptimizer(
            objective_function=objective_function,
            parameter_bounds=parameter_bounds,
            max_iterations=self.config.get('max_iterations', 100),
            max_function_evaluations=self.config.get('max_function_evaluations', 2000),
            max_time_seconds=self.config.get('max_time_seconds', 300),
            num_complexes=self.config.get('num_complexes', 5),
            convergence_threshold=self.config.get('convergence_threshold', 1e-6),
            use_parallel=self.config.get('use_parallel', True)
        )
        
        # 执行优化
        result = optimizer.optimize()
        
        if result['converged'] or result['best_value'] < np.inf:
            # 设置最优参数
            best_params = result['best_parameters']
            interpolator.nugget = best_params[0]
            interpolator.sill = best_params[1]
            interpolator.range_param = best_params[2]
            
            self.logger.info(f"Kriging参数优化完成，最优目标值: {result['best_value']:.6f}")
        else:
            self.logger.warning("Kriging参数优化失败，使用默认参数")
        
        return result
    
    def optimize_oi_parameters(self, interpolator, station_coords: np.ndarray, 
                             station_values: np.ndarray) -> Dict[str, Any]:
        """
        优化OI插值器参数
        
        Args:
            interpolator: OI插值器实例
            station_coords: 站点坐标
            station_values: 站点值
            
        Returns:
            优化结果
        """
        self.logger.info("开始优化OI参数")
        
        # 定义参数边界
        parameter_bounds = [
            (np.var(station_values) * 0.1, np.var(station_values) * 2),  # variance
            (0.01, 2.0),  # correlation_length
            (0.0, np.var(station_values) * 0.5)  # observation_error_variance
        ]
        
        # 定义目标函数
        def objective_function(params):
            variance, correlation_length, obs_error_var = params
            
            # 设置参数
            interpolator.variance = variance
            interpolator.correlation_length = correlation_length
            interpolator.observation_error_variance = obs_error_var
            
            # 交叉验证
            return self._cross_validate_interpolator(
                interpolator, station_coords, station_values
            )
        
        # 创建SCE-UA优化器
        optimizer = SCEUAOptimizer(
            objective_function=objective_function,
            parameter_bounds=parameter_bounds,
            max_iterations=self.config.get('max_iterations', 100),
            max_function_evaluations=self.config.get('max_function_evaluations', 2000),
            max_time_seconds=self.config.get('max_time_seconds', 300),
            num_complexes=self.config.get('num_complexes', 5),
            convergence_threshold=self.config.get('convergence_threshold', 1e-6),
            use_parallel=self.config.get('use_parallel', True)
        )
        
        # 执行优化
        result = optimizer.optimize()
        
        if result['converged'] or result['best_value'] < np.inf:
            # 设置最优参数
            best_params = result['best_parameters']
            interpolator.variance = best_params[0]
            interpolator.correlation_length = best_params[1]
            interpolator.observation_error_variance = best_params[2]
            
            self.logger.info(f"OI参数优化完成，最优目标值: {result['best_value']:.6f}")
        else:
            self.logger.warning("OI参数优化失败，使用默认参数")
        
        return result
    
    def optimize_prism_parameters(self, interpolator, station_coords: np.ndarray, 
                                station_values: np.ndarray) -> Dict[str, Any]:
        """
        优化PRISM插值器参数
        
        Args:
            interpolator: PRISM插值器实例
            station_coords: 站点坐标
            station_values: 站点值
            
        Returns:
            优化结果
        """
        self.logger.info("开始优化PRISM参数")
        
        # 定义参数边界
        parameter_bounds = [
            (0.1, 1.0),  # elevation_weight
            (0.0, 1.0),  # slope_weight
            (0.0, 1.0),  # aspect_weight
            (1.0, 5.0)   # distance_decay
        ]
        
        # 定义目标函数
        def objective_function(params):
            elevation_weight, slope_weight, aspect_weight, distance_decay = params
            
            # 设置参数
            interpolator.elevation_weight = elevation_weight
            interpolator.slope_weight = slope_weight
            interpolator.aspect_weight = aspect_weight
            interpolator.distance_decay = distance_decay
            
            # 交叉验证
            return self._cross_validate_interpolator(
                interpolator, station_coords, station_values
            )
        
        # 创建SCE-UA优化器
        optimizer = SCEUAOptimizer(
            objective_function=objective_function,
            parameter_bounds=parameter_bounds,
            max_iterations=self.config.get('max_iterations', 100),
            max_function_evaluations=self.config.get('max_function_evaluations', 2000),
            max_time_seconds=self.config.get('max_time_seconds', 300),
            num_complexes=self.config.get('num_complexes', 5),
            convergence_threshold=self.config.get('convergence_threshold', 1e-6),
            use_parallel=self.config.get('use_parallel', True)
        )
        
        # 执行优化
        result = optimizer.optimize()
        
        if result['converged'] or result['best_value'] < np.inf:
            # 设置最优参数
            best_params = result['best_parameters']
            interpolator.elevation_weight = best_params[0]
            interpolator.slope_weight = best_params[1]
            interpolator.aspect_weight = best_params[2]
            interpolator.distance_decay = best_params[3]
            
            self.logger.info(f"PRISM参数优化完成，最优目标值: {result['best_value']:.6f}")
        else:
            self.logger.warning("PRISM参数优化失败，使用默认参数")
        
        return result
    
    def _cross_validate_interpolator(self, interpolator, station_coords: np.ndarray, 
                                   station_values: np.ndarray) -> float:
        """
        对插值器进行交叉验证
        
        Args:
            interpolator: 插值器实例
            station_coords: 站点坐标
            station_values: 站点值
            
        Returns:
            交叉验证误差（RMSE）
        """
        n_stations = len(station_coords)
        errors = []
        
        # 留一法交叉验证
        for i in range(n_stations):
            try:
                # 排除第i个站点
                train_coords = np.delete(station_coords, i, axis=0)
                train_values = np.delete(station_values, i)
                
                # 拟合插值器
                interpolator.fit(train_coords, train_values)
                
                # 预测第i个站点
                target_coord = station_coords[i:i+1]
                
                if hasattr(interpolator, 'predict'):
                    if 'kriging' in interpolator.__class__.__name__.lower() or \
                       'oi' in interpolator.__class__.__name__.lower():
                        # Kriging和OI返回预测值和方差
                        predictions, _ = interpolator.predict(target_coord)
                        predicted_value = predictions[0]
                    else:
                        # 其他方法只返回预测值
                        predictions = interpolator.predict(target_coord)
                        predicted_value = predictions[0]
                else:
                    # 如果没有predict方法，跳过
                    continue
                
                observed_value = station_values[i]
                
                if not np.isnan(predicted_value):
                    errors.append((observed_value - predicted_value) ** 2)
                    
            except Exception as e:
                # 如果某次交叉验证失败，跳过
                self.logger.debug(f"交叉验证第{i}次失败: {e}")
                continue
        
        if errors:
            rmse = np.sqrt(np.mean(errors))
            return rmse
        else:
            return np.inf

# -*- coding: utf-8 -*-
"""
SCE-UA参数优化算法模块
Shuffled Complex Evolution - University of Arizona
用于优化插值方法的参数
"""

import numpy as np
from typing import Callable, List, Tuple, Dict, Any, Optional
import logging
import time
from concurrent.futures import ProcessPoolExecutor
import multiprocessing as mp

class SCEUAOptimizer:
    """SCE-UA优化器"""
    
    def __init__(self, 
                 objective_function: Callable,
                 parameter_bounds: List[Tuple[float, float]],
                 max_iterations: int = 1000,
                 max_function_evaluations: int = 10000,
                 num_complexes: int = 5,
                 points_per_complex: int = None,
                 points_per_subcomplex: int = None,
                 alpha: float = 1.0,
                 beta: float = 0.5,
                 convergence_threshold: float = 1e-6,
                 max_time_seconds: Optional[int] = None,
                 use_parallel: bool = True):
        """
        初始化SCE-UA优化器
        
        Args:
            objective_function: 目标函数，输入参数数组，返回目标值（越小越好）
            parameter_bounds: 参数边界列表，每个元素为(min, max)
            max_iterations: 最大迭代次数
            max_function_evaluations: 最大函数评估次数
            num_complexes: 复合形数量
            points_per_complex: 每个复合形的点数
            points_per_subcomplex: 每个子复合形的点数
            alpha: 反射系数
            beta: 收缩系数
            convergence_threshold: 收敛阈值
            max_time_seconds: 最大运行时间（秒）
            use_parallel: 是否使用并行计算
        """
        self.objective_function = objective_function
        self.parameter_bounds = parameter_bounds
        self.num_parameters = len(parameter_bounds)
        self.max_iterations = max_iterations
        self.max_function_evaluations = max_function_evaluations
        self.num_complexes = num_complexes
        self.convergence_threshold = convergence_threshold
        self.max_time_seconds = max_time_seconds
        self.use_parallel = use_parallel
        
        # 设置默认值
        if points_per_complex is None:
            self.points_per_complex = 2 * self.num_parameters + 1
        else:
            self.points_per_complex = points_per_complex
            
        if points_per_subcomplex is None:
            self.points_per_subcomplex = self.num_parameters + 1
        else:
            self.points_per_subcomplex = points_per_subcomplex
        
        self.alpha = alpha
        self.beta = beta
        
        # 总点数
        self.total_points = self.num_complexes * self.points_per_complex
        
        self.logger = logging.getLogger(__name__)
        
        # 优化历史
        self.history = {
            'best_parameters': [],
            'best_values': [],
            'function_evaluations': 0,
            'iterations': 0
        }
    
    def generate_initial_population(self) -> np.ndarray:
        """
        生成初始种群
        
        Returns:
            初始种群数组，形状为(total_points, num_parameters)
        """
        population = np.zeros((self.total_points, self.num_parameters))
        
        for i in range(self.num_parameters):
            min_val, max_val = self.parameter_bounds[i]
            population[:, i] = np.random.uniform(min_val, max_val, self.total_points)
        
        return population
    
    def evaluate_population(self, population: np.ndarray) -> np.ndarray:
        """
        评估种群
        
        Args:
            population: 种群数组
            
        Returns:
            目标函数值数组
        """
        if self.use_parallel and len(population) > 1:
            try:
                with ProcessPoolExecutor(max_workers=min(mp.cpu_count(), len(population))) as executor:
                    futures = [executor.submit(self.objective_function, individual) 
                             for individual in population]
                    values = [future.result() for future in futures]
                return np.array(values)
            except Exception as e:
                self.logger.warning(f"并行计算失败，使用串行计算: {e}")
        
        # 串行计算
        values = []
        for individual in population:
            try:
                value = self.objective_function(individual)
                values.append(value)
            except Exception as e:
                self.logger.warning(f"目标函数评估失败: {e}")
                values.append(np.inf)
        
        return np.array(values)
    
    def sort_population(self, population: np.ndarray, values: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        按目标函数值排序种群
        
        Args:
            population: 种群数组
            values: 目标函数值数组
            
        Returns:
            排序后的(种群, 目标函数值)
        """
        sorted_indices = np.argsort(values)
        return population[sorted_indices], values[sorted_indices]
    
    def partition_into_complexes(self, population: np.ndarray, values: np.ndarray) -> List[Tuple[np.ndarray, np.ndarray]]:
        """
        将种群分割为复合形
        
        Args:
            population: 排序后的种群
            values: 排序后的目标函数值
            
        Returns:
            复合形列表，每个元素为(复合形种群, 复合形目标值)
        """
        complexes = []
        
        for k in range(self.num_complexes):
            # 选择属于第k个复合形的点
            complex_indices = []
            for i in range(self.points_per_complex):
                index = k + i * self.num_complexes
                if index < len(population):
                    complex_indices.append(index)
            
            if complex_indices:
                complex_population = population[complex_indices]
                complex_values = values[complex_indices]
                complexes.append((complex_population, complex_values))
        
        return complexes
    
    def evolve_complex(self, complex_population: np.ndarray, complex_values: np.ndarray) -> Tuple[np.ndarray, np.ndarray, int]:
        """
        进化单个复合形
        
        Args:
            complex_population: 复合形种群
            complex_values: 复合形目标值
            
        Returns:
            (进化后的种群, 进化后的目标值, 函数评估次数)
        """
        function_evaluations = 0
        
        # 选择子复合形
        subcomplex_indices = np.random.choice(
            len(complex_population), 
            size=min(self.points_per_subcomplex, len(complex_population)), 
            replace=False
        )
        
        subcomplex_population = complex_population[subcomplex_indices].copy()
        subcomplex_values = complex_values[subcomplex_indices].copy()
        
        # 排序子复合形
        sorted_indices = np.argsort(subcomplex_values)
        subcomplex_population = subcomplex_population[sorted_indices]
        subcomplex_values = subcomplex_values[sorted_indices]
        
        # 计算质心（除了最差点）
        centroid = np.mean(subcomplex_population[:-1], axis=0)
        
        # 反射
        worst_point = subcomplex_population[-1]
        reflected_point = centroid + self.alpha * (centroid - worst_point)
        
        # 确保在边界内
        reflected_point = self.enforce_bounds(reflected_point)
        
        # 评估反射点
        try:
            reflected_value = self.objective_function(reflected_point)
            function_evaluations += 1
        except:
            reflected_value = np.inf
        
        # 如果反射点更好，替换最差点
        if reflected_value < subcomplex_values[-1]:
            subcomplex_population[-1] = reflected_point
            subcomplex_values[-1] = reflected_value
        else:
            # 收缩
            contracted_point = worst_point + self.beta * (centroid - worst_point)
            contracted_point = self.enforce_bounds(contracted_point)
            
            try:
                contracted_value = self.objective_function(contracted_point)
                function_evaluations += 1
            except:
                contracted_value = np.inf
            
            if contracted_value < subcomplex_values[-1]:
                subcomplex_population[-1] = contracted_point
                subcomplex_values[-1] = contracted_value
            else:
                # 随机生成新点
                new_point = self.generate_random_point()
                try:
                    new_value = self.objective_function(new_point)
                    function_evaluations += 1
                except:
                    new_value = np.inf
                
                subcomplex_population[-1] = new_point
                subcomplex_values[-1] = new_value
        
        # 将子复合形的改进传回复合形
        for i, idx in enumerate(subcomplex_indices):
            complex_population[idx] = subcomplex_population[sorted_indices[i]]
            complex_values[idx] = subcomplex_values[sorted_indices[i]]
        
        return complex_population, complex_values, function_evaluations
    
    def enforce_bounds(self, point: np.ndarray) -> np.ndarray:
        """
        确保点在参数边界内
        
        Args:
            point: 参数点
            
        Returns:
            边界内的参数点
        """
        bounded_point = point.copy()
        
        for i, (min_val, max_val) in enumerate(self.parameter_bounds):
            bounded_point[i] = np.clip(bounded_point[i], min_val, max_val)
        
        return bounded_point
    
    def generate_random_point(self) -> np.ndarray:
        """
        生成随机参数点
        
        Returns:
            随机参数点
        """
        point = np.zeros(self.num_parameters)
        
        for i, (min_val, max_val) in enumerate(self.parameter_bounds):
            point[i] = np.random.uniform(min_val, max_val)
        
        return point
    
    def check_convergence(self, values: np.ndarray) -> bool:
        """
        检查收敛性
        
        Args:
            values: 当前目标函数值数组
            
        Returns:
            是否收敛
        """
        if len(values) < 2:
            return False
        
        # 计算相对标准差
        mean_val = np.mean(values)
        if mean_val == 0:
            return np.std(values) < self.convergence_threshold
        
        relative_std = np.std(values) / abs(mean_val)
        return relative_std < self.convergence_threshold

    def optimize(self) -> Dict[str, Any]:
        """
        执行SCE-UA优化

        Returns:
            优化结果字典
        """
        start_time = time.time()
        self.logger.info("开始SCE-UA优化")

        # 生成初始种群
        population = self.generate_initial_population()
        values = self.evaluate_population(population)
        self.history['function_evaluations'] += len(values)

        # 排序
        population, values = self.sort_population(population, values)

        # 记录最佳结果
        self.history['best_parameters'].append(population[0].copy())
        self.history['best_values'].append(values[0])

        self.logger.info(f"初始最佳目标值: {values[0]:.6f}")

        # 主优化循环
        for iteration in range(self.max_iterations):
            self.history['iterations'] = iteration + 1

            # 检查时间限制
            if self.max_time_seconds and (time.time() - start_time) > self.max_time_seconds:
                self.logger.info(f"达到时间限制 {self.max_time_seconds} 秒，停止优化")
                break

            # 检查函数评估次数限制
            if self.history['function_evaluations'] >= self.max_function_evaluations:
                self.logger.info(f"达到函数评估次数限制 {self.max_function_evaluations}，停止优化")
                break

            # 检查收敛性
            if self.check_convergence(values):
                self.logger.info(f"在第 {iteration + 1} 次迭代达到收敛")
                break

            # 分割为复合形
            complexes = self.partition_into_complexes(population, values)

            # 进化每个复合形
            new_population = []
            new_values = []

            for complex_pop, complex_vals in complexes:
                evolved_pop, evolved_vals, func_evals = self.evolve_complex(complex_pop, complex_vals)
                self.history['function_evaluations'] += func_evals

                new_population.append(evolved_pop)
                new_values.append(evolved_vals)

            # 合并所有复合形
            if new_population:
                population = np.vstack(new_population)
                values = np.concatenate(new_values)

                # 排序
                population, values = self.sort_population(population, values)

                # 记录最佳结果
                self.history['best_parameters'].append(population[0].copy())
                self.history['best_values'].append(values[0])

                # 每10次迭代输出一次进度
                if (iteration + 1) % 10 == 0:
                    self.logger.info(f"迭代 {iteration + 1}: 最佳目标值 = {values[0]:.6f}, "
                                   f"函数评估次数 = {self.history['function_evaluations']}")

        # 优化完成
        end_time = time.time()
        optimization_time = end_time - start_time

        result = {
            'best_parameters': population[0].copy(),
            'best_value': values[0],
            'iterations': self.history['iterations'],
            'function_evaluations': self.history['function_evaluations'],
            'optimization_time': optimization_time,
            'converged': self.check_convergence(values),
            'history': self.history.copy()
        }

        self.logger.info(f"SCE-UA优化完成:")
        self.logger.info(f"  最佳参数: {result['best_parameters']}")
        self.logger.info(f"  最佳目标值: {result['best_value']:.6f}")
        self.logger.info(f"  迭代次数: {result['iterations']}")
        self.logger.info(f"  函数评估次数: {result['function_evaluations']}")
        self.logger.info(f"  优化时间: {optimization_time:.2f} 秒")
        self.logger.info(f"  是否收敛: {result['converged']}")

        return result

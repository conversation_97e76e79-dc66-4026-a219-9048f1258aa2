# -*- coding: utf-8 -*-
"""
IDW (反距离权重插值) 实现
Inverse Distance Weighting Interpolation
"""

import numpy as np
from typing import List, Tuple, Dict, Any, Optional
import logging
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp
from scipy.spatial.distance import cdist
import gc

class IDWInterpolator:
    """IDW插值器"""
    
    def __init__(self, 
                 power: float = 2.0,
                 search_radius: float = 0.5,
                 min_neighbors: int = 3,
                 max_neighbors: int = 12,
                 use_anisotropy: bool = False,
                 anisotropy_angle: float = 0.0,
                 anisotropy_ratio: float = 1.0,
                 use_parallel: bool = True,
                 chunk_size: int = 1000):
        """
        初始化IDW插值器
        
        Args:
            power: 距离权重指数，通常为2
            search_radius: 搜索半径（度）
            min_neighbors: 最小邻近点数
            max_neighbors: 最大邻近点数
            use_anisotropy: 是否使用各向异性
            anisotropy_angle: 各向异性角度（度）
            anisotropy_ratio: 各向异性比率
            use_parallel: 是否使用并行计算
            chunk_size: 并行计算时的分块大小
        """
        self.power = power
        self.search_radius = search_radius
        self.min_neighbors = min_neighbors
        self.max_neighbors = max_neighbors
        self.use_anisotropy = use_anisotropy
        self.anisotropy_angle = np.radians(anisotropy_angle)
        self.anisotropy_ratio = anisotropy_ratio
        self.use_parallel = use_parallel
        self.chunk_size = chunk_size
        
        self.logger = logging.getLogger(__name__)
        
        # 缓存
        self._station_coords = None
        self._station_values = None
    
    def fit(self, station_coords: np.ndarray, station_values: np.ndarray):
        """
        拟合插值器（IDW不需要训练，只是存储数据）
        
        Args:
            station_coords: 站点坐标数组，形状为(n_stations, 2) [经度, 纬度]
            station_values: 站点观测值数组，形状为(n_stations,)
        """
        self._station_coords = np.array(station_coords)
        self._station_values = np.array(station_values)
        
        # 移除NaN值
        valid_mask = ~np.isnan(self._station_values)
        self._station_coords = self._station_coords[valid_mask]
        self._station_values = self._station_values[valid_mask]
        
        self.logger.info(f"IDW插值器已准备就绪，使用 {len(self._station_values)} 个有效站点")
    
    def predict(self, target_coords: np.ndarray) -> np.ndarray:
        """
        执行IDW插值预测
        
        Args:
            target_coords: 目标点坐标数组，形状为(n_targets, 2) [经度, 纬度]
            
        Returns:
            插值结果数组，形状为(n_targets,)
        """
        if self._station_coords is None or self._station_values is None:
            raise ValueError("插值器尚未拟合，请先调用fit方法")
        
        target_coords = np.array(target_coords)
        n_targets = len(target_coords)
        
        self.logger.info(f"开始IDW插值，目标点数: {n_targets}")
        
        if self.use_parallel and n_targets > self.chunk_size:
            # 并行计算
            predictions = self._predict_parallel(target_coords)
        else:
            # 串行计算
            predictions = self._predict_serial(target_coords)
        
        self.logger.info("IDW插值完成")
        return predictions
    
    def _predict_serial(self, target_coords: np.ndarray) -> np.ndarray:
        """
        串行预测
        
        Args:
            target_coords: 目标点坐标
            
        Returns:
            预测结果
        """
        predictions = np.full(len(target_coords), np.nan)
        
        for i, target_coord in enumerate(target_coords):
            predictions[i] = self._interpolate_single_point(target_coord)
            
            # 每处理1000个点输出一次进度
            if (i + 1) % 1000 == 0:
                self.logger.info(f"已处理 {i + 1}/{len(target_coords)} 个点")
        
        return predictions
    
    def _predict_parallel(self, target_coords: np.ndarray) -> np.ndarray:
        """
        并行预测
        
        Args:
            target_coords: 目标点坐标
            
        Returns:
            预测结果
        """
        n_targets = len(target_coords)
        predictions = np.full(n_targets, np.nan)
        
        # 分块处理
        chunks = []
        for i in range(0, n_targets, self.chunk_size):
            end_idx = min(i + self.chunk_size, n_targets)
            chunks.append((i, target_coords[i:end_idx]))
        
        self.logger.info(f"使用并行计算，分为 {len(chunks)} 个块")
        
        # 并行处理
        max_workers = min(mp.cpu_count(), len(chunks))
        
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_chunk = {}
            for chunk_idx, (start_idx, chunk_coords) in enumerate(chunks):
                future = executor.submit(
                    self._process_chunk,
                    chunk_coords,
                    self._station_coords,
                    self._station_values,
                    self.power,
                    self.search_radius,
                    self.min_neighbors,
                    self.max_neighbors,
                    self.use_anisotropy,
                    self.anisotropy_angle,
                    self.anisotropy_ratio
                )
                future_to_chunk[future] = (chunk_idx, start_idx, len(chunk_coords))
            
            # 收集结果
            completed = 0
            for future in as_completed(future_to_chunk):
                chunk_idx, start_idx, chunk_size = future_to_chunk[future]
                try:
                    chunk_predictions = future.result()
                    end_idx = start_idx + chunk_size
                    predictions[start_idx:end_idx] = chunk_predictions
                    
                    completed += 1
                    self.logger.info(f"完成块 {completed}/{len(chunks)}")
                    
                except Exception as e:
                    self.logger.error(f"处理块 {chunk_idx} 时出错: {e}")
                    # 对失败的块使用串行处理
                    chunk_coords = chunks[chunk_idx][1]
                    for j, coord in enumerate(chunk_coords):
                        predictions[start_idx + j] = self._interpolate_single_point(coord)
        
        return predictions
    
    @staticmethod
    def _process_chunk(chunk_coords: np.ndarray,
                      station_coords: np.ndarray,
                      station_values: np.ndarray,
                      power: float,
                      search_radius: float,
                      min_neighbors: int,
                      max_neighbors: int,
                      use_anisotropy: bool,
                      anisotropy_angle: float,
                      anisotropy_ratio: float) -> np.ndarray:
        """
        处理单个数据块（静态方法，用于并行计算）
        
        Args:
            chunk_coords: 块坐标
            station_coords: 站点坐标
            station_values: 站点值
            power: 距离权重指数
            search_radius: 搜索半径
            min_neighbors: 最小邻近点数
            max_neighbors: 最大邻近点数
            use_anisotropy: 是否使用各向异性
            anisotropy_angle: 各向异性角度
            anisotropy_ratio: 各向异性比率
            
        Returns:
            块预测结果
        """
        chunk_predictions = np.full(len(chunk_coords), np.nan)
        
        for i, target_coord in enumerate(chunk_coords):
            # 计算距离
            if use_anisotropy:
                distances = IDWInterpolator._calculate_anisotropic_distances(
                    target_coord, station_coords, anisotropy_angle, anisotropy_ratio
                )
            else:
                distances = IDWInterpolator._calculate_euclidean_distances(
                    target_coord, station_coords
                )
            
            # 筛选邻近点
            valid_indices = distances <= search_radius
            if np.sum(valid_indices) < min_neighbors:
                # 如果搜索半径内点数不足，选择最近的点
                sorted_indices = np.argsort(distances)
                valid_indices = sorted_indices[:min_neighbors] if len(sorted_indices) >= min_neighbors else sorted_indices
            else:
                # 限制最大邻近点数
                valid_distances = distances[valid_indices]
                if len(valid_distances) > max_neighbors:
                    sorted_valid_indices = np.argsort(valid_distances)[:max_neighbors]
                    temp_indices = np.where(valid_indices)[0]
                    valid_indices = np.zeros_like(valid_indices, dtype=bool)
                    valid_indices[temp_indices[sorted_valid_indices]] = True
            
            if isinstance(valid_indices, np.ndarray) and valid_indices.dtype == bool:
                neighbor_distances = distances[valid_indices]
                neighbor_values = station_values[valid_indices]
            else:
                neighbor_distances = distances[valid_indices]
                neighbor_values = station_values[valid_indices]
            
            # IDW插值计算
            if len(neighbor_distances) > 0:
                # 处理零距离情况
                zero_distance_mask = neighbor_distances < 1e-10
                if np.any(zero_distance_mask):
                    # 如果有站点与目标点重合，直接使用该站点的值
                    chunk_predictions[i] = neighbor_values[zero_distance_mask][0]
                else:
                    # 计算权重
                    weights = 1.0 / (neighbor_distances ** power)
                    weights_sum = np.sum(weights)
                    
                    if weights_sum > 0:
                        chunk_predictions[i] = np.sum(weights * neighbor_values) / weights_sum
        
        return chunk_predictions

    def _interpolate_single_point(self, target_coord: np.ndarray) -> float:
        """
        对单个点进行IDW插值

        Args:
            target_coord: 目标点坐标 [经度, 纬度]

        Returns:
            插值结果
        """
        # 计算距离
        if self.use_anisotropy:
            distances = self._calculate_anisotropic_distances(
                target_coord, self._station_coords,
                self.anisotropy_angle, self.anisotropy_ratio
            )
        else:
            distances = self._calculate_euclidean_distances(
                target_coord, self._station_coords
            )

        # 筛选邻近点
        valid_indices = distances <= self.search_radius
        if np.sum(valid_indices) < self.min_neighbors:
            # 如果搜索半径内点数不足，选择最近的点
            sorted_indices = np.argsort(distances)
            if len(sorted_indices) >= self.min_neighbors:
                valid_indices = sorted_indices[:self.min_neighbors]
            else:
                valid_indices = sorted_indices
        else:
            # 限制最大邻近点数
            valid_distances = distances[valid_indices]
            if len(valid_distances) > self.max_neighbors:
                sorted_valid_indices = np.argsort(valid_distances)[:self.max_neighbors]
                temp_indices = np.where(valid_indices)[0]
                valid_indices = np.zeros_like(valid_indices, dtype=bool)
                valid_indices[temp_indices[sorted_valid_indices]] = True

        if isinstance(valid_indices, np.ndarray) and valid_indices.dtype == bool:
            neighbor_distances = distances[valid_indices]
            neighbor_values = self._station_values[valid_indices]
        else:
            neighbor_distances = distances[valid_indices]
            neighbor_values = self._station_values[valid_indices]

        # IDW插值计算
        if len(neighbor_distances) == 0:
            return np.nan

        # 处理零距离情况
        zero_distance_mask = neighbor_distances < 1e-10
        if np.any(zero_distance_mask):
            # 如果有站点与目标点重合，直接使用该站点的值
            return neighbor_values[zero_distance_mask][0]

        # 计算权重
        weights = 1.0 / (neighbor_distances ** self.power)
        weights_sum = np.sum(weights)

        if weights_sum > 0:
            return np.sum(weights * neighbor_values) / weights_sum
        else:
            return np.nan

    @staticmethod
    def _calculate_euclidean_distances(target_coord: np.ndarray,
                                     station_coords: np.ndarray) -> np.ndarray:
        """
        计算欧几里得距离

        Args:
            target_coord: 目标点坐标
            station_coords: 站点坐标数组

        Returns:
            距离数组
        """
        return cdist([target_coord], station_coords)[0]

    @staticmethod
    def _calculate_anisotropic_distances(target_coord: np.ndarray,
                                       station_coords: np.ndarray,
                                       anisotropy_angle: float,
                                       anisotropy_ratio: float) -> np.ndarray:
        """
        计算各向异性距离

        Args:
            target_coord: 目标点坐标
            station_coords: 站点坐标数组
            anisotropy_angle: 各向异性角度（弧度）
            anisotropy_ratio: 各向异性比率

        Returns:
            各向异性距离数组
        """
        # 计算相对坐标
        dx = station_coords[:, 0] - target_coord[0]
        dy = station_coords[:, 1] - target_coord[1]

        # 旋转坐标系
        cos_angle = np.cos(anisotropy_angle)
        sin_angle = np.sin(anisotropy_angle)

        dx_rot = dx * cos_angle + dy * sin_angle
        dy_rot = -dx * sin_angle + dy * cos_angle

        # 应用各向异性比率
        dx_scaled = dx_rot
        dy_scaled = dy_rot / anisotropy_ratio

        # 计算距离
        distances = np.sqrt(dx_scaled**2 + dy_scaled**2)

        return distances

    def get_parameters(self) -> Dict[str, Any]:
        """
        获取当前参数

        Returns:
            参数字典
        """
        return {
            'power': self.power,
            'search_radius': self.search_radius,
            'min_neighbors': self.min_neighbors,
            'max_neighbors': self.max_neighbors,
            'use_anisotropy': self.use_anisotropy,
            'anisotropy_angle': np.degrees(self.anisotropy_angle),
            'anisotropy_ratio': self.anisotropy_ratio
        }

    def set_parameters(self, **params):
        """
        设置参数

        Args:
            **params: 参数字典
        """
        for key, value in params.items():
            if hasattr(self, key):
                if key == 'anisotropy_angle':
                    setattr(self, key, np.radians(value))
                else:
                    setattr(self, key, value)

    def clear_cache(self):
        """清理缓存"""
        self._station_coords = None
        self._station_values = None
        gc.collect()

# -*- coding: utf-8 -*-
"""
评价指标模块
计算插值结果的各种评价指标
"""

import numpy as np
from typing import List, Dict, Any
import logging
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

class EvaluationMetrics:
    """评价指标计算器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def calculate_mae(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """
        计算平均绝对误差 (Mean Absolute Error)
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            MAE值
        """
        # 移除NaN值
        mask = ~(np.isnan(observed) | np.isnan(predicted))
        if np.sum(mask) == 0:
            return np.nan
        
        return mean_absolute_error(observed[mask], predicted[mask])
    
    def calculate_rmse(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """
        计算均方根误差 (Root Mean Square Error)
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            RMSE值
        """
        # 移除NaN值
        mask = ~(np.isnan(observed) | np.isnan(predicted))
        if np.sum(mask) == 0:
            return np.nan
        
        return np.sqrt(mean_squared_error(observed[mask], predicted[mask]))
    
    def calculate_r2(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """
        计算决定系数 (R-squared)
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            R²值
        """
        # 移除NaN值
        mask = ~(np.isnan(observed) | np.isnan(predicted))
        if np.sum(mask) < 2:  # 至少需要2个点
            return np.nan
        
        return r2_score(observed[mask], predicted[mask])
    
    def calculate_nse(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """
        计算纳什效率系数 (Nash-Sutcliffe Efficiency)
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            NSE值
        """
        # 移除NaN值
        mask = ~(np.isnan(observed) | np.isnan(predicted))
        if np.sum(mask) < 2:
            return np.nan
        
        obs_valid = observed[mask]
        pred_valid = predicted[mask]
        
        # 计算观测值的平均值
        obs_mean = np.mean(obs_valid)
        
        # 计算NSE
        numerator = np.sum((obs_valid - pred_valid) ** 2)
        denominator = np.sum((obs_valid - obs_mean) ** 2)
        
        if denominator == 0:
            return np.nan
        
        nse = 1 - (numerator / denominator)
        return nse
    
    def calculate_bias(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """
        计算偏差 (Bias)
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            偏差值
        """
        # 移除NaN值
        mask = ~(np.isnan(observed) | np.isnan(predicted))
        if np.sum(mask) == 0:
            return np.nan
        
        return np.mean(predicted[mask] - observed[mask])
    
    def calculate_relative_bias(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """
        计算相对偏差 (Relative Bias)
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            相对偏差值 (%)
        """
        # 移除NaN值
        mask = ~(np.isnan(observed) | np.isnan(predicted))
        if np.sum(mask) == 0:
            return np.nan
        
        obs_valid = observed[mask]
        pred_valid = predicted[mask]
        
        obs_mean = np.mean(obs_valid)
        if obs_mean == 0:
            return np.nan
        
        bias = np.mean(pred_valid - obs_valid)
        relative_bias = (bias / obs_mean) * 100
        
        return relative_bias
    
    def calculate_correlation(self, observed: np.ndarray, predicted: np.ndarray) -> float:
        """
        计算相关系数
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            相关系数
        """
        # 移除NaN值
        mask = ~(np.isnan(observed) | np.isnan(predicted))
        if np.sum(mask) < 2:
            return np.nan
        
        return np.corrcoef(observed[mask], predicted[mask])[0, 1]
    
    def calculate_all_metrics(self, observed: np.ndarray, predicted: np.ndarray) -> Dict[str, float]:
        """
        计算所有评价指标
        
        Args:
            observed: 观测值数组
            predicted: 预测值数组
            
        Returns:
            包含所有指标的字典
        """
        metrics = {
            'MAE': self.calculate_mae(observed, predicted),
            'RMSE': self.calculate_rmse(observed, predicted),
            'R2': self.calculate_r2(observed, predicted),
            'NSE': self.calculate_nse(observed, predicted),
            'Bias': self.calculate_bias(observed, predicted),
            'Relative_Bias': self.calculate_relative_bias(observed, predicted),
            'Correlation': self.calculate_correlation(observed, predicted)
        }
        
        # 记录有效数据点数
        mask = ~(np.isnan(observed) | np.isnan(predicted))
        metrics['Valid_Points'] = int(np.sum(mask))
        
        return metrics
    
    def format_metrics_report(self, metrics: Dict[str, float], method_name: str = "") -> str:
        """
        格式化指标报告
        
        Args:
            metrics: 指标字典
            method_name: 方法名称
            
        Returns:
            格式化的报告字符串
        """
        report = []
        if method_name:
            report.append(f"=== {method_name} 插值评价指标 ===")
        else:
            report.append("=== 插值评价指标 ===")
        
        report.append(f"有效数据点数: {metrics.get('Valid_Points', 'N/A')}")
        report.append(f"平均绝对误差 (MAE): {metrics.get('MAE', np.nan):.4f}")
        report.append(f"均方根误差 (RMSE): {metrics.get('RMSE', np.nan):.4f}")
        report.append(f"决定系数 (R²): {metrics.get('R2', np.nan):.4f}")
        report.append(f"纳什效率系数 (NSE): {metrics.get('NSE', np.nan):.4f}")
        report.append(f"偏差 (Bias): {metrics.get('Bias', np.nan):.4f}")
        report.append(f"相对偏差 (%): {metrics.get('Relative_Bias', np.nan):.2f}")
        report.append(f"相关系数: {metrics.get('Correlation', np.nan):.4f}")
        
        return "\n".join(report)
    
    def is_good_performance(self, metrics: Dict[str, float]) -> bool:
        """
        判断插值性能是否良好
        
        Args:
            metrics: 指标字典
            
        Returns:
            是否性能良好
        """
        # 简单的性能判断标准
        nse = metrics.get('NSE', -999)
        r2 = metrics.get('R2', -999)
        
        # NSE > 0.5 且 R² > 0.6 认为性能良好
        return nse > 0.5 and r2 > 0.6

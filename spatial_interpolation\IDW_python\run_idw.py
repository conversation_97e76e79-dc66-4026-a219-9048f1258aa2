# -*- coding: utf-8 -*-
"""
IDW插值主运行脚本
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from common.data_processor import DataProcessor
from common.raster_utils import RasterUtils
from common.evaluation_metrics import EvaluationMetrics
from config.config_manager import ConfigManager
from IDW_python.idw_interpolator import IDWInterpolator

class IDWRunner:
    """IDW插值运行器"""
    
    def __init__(self, config_file: str = None):
        """
        初始化运行器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = ConfigManager(config_file)
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.data_processor = DataProcessor(self.config.get('paths.base_path'))
        self.raster_utils = RasterUtils()
        self.evaluator = EvaluationMetrics()
        
        # 加载地形数据
        self._load_terrain_data()
        
        # 结果存储
        self.results = []
    
    def _load_terrain_data(self):
        """加载地形数据"""
        terrain_dir = self.config.get('paths.terrain_dir')
        
        # 加载掩膜
        mask_file = os.path.join(terrain_dir, 'mask.asc')
        self.mask_data, self.mask_header = self.raster_utils.read_asc_file(mask_file)
        self.mask = self.raster_utils.create_mask_from_template(self.mask_data)
        
        # 生成栅格坐标
        self.grid_x, self.grid_y = self.raster_utils.get_coordinates_from_header(self.mask_header)
        
        self.logger.info(f"地形数据加载完成，栅格尺寸: {self.mask_data.shape}")
    
    def run_single_event(self, flood_event: str) -> Dict[str, Any]:
        """
        运行单个洪水场次的IDW插值
        
        Args:
            flood_event: 洪水场次名称
            
        Returns:
            运行结果字典
        """
        self.logger.info(f"开始处理洪水场次: {flood_event}")
        
        # 加载数据
        rainfall_data = self.data_processor.load_rainfall_data(flood_event)
        interp_info = self.data_processor.get_interpolation_info(flood_event)
        stations_info = self.data_processor.load_stations()
        
        if interp_info.empty:
            self.logger.warning(f"洪水场次 {flood_event} 没有插值信息")
            return {'flood_event': flood_event, 'status': 'no_interpolation_info'}
        
        # 过滤有效时刻
        valid_times = self.data_processor.filter_zero_rainfall_times(
            rainfall_data, 
            self.config.get('data_processing.min_nonzero_stations', 3)
        )
        
        if not valid_times:
            self.logger.warning(f"洪水场次 {flood_event} 没有有效时刻")
            return {'flood_event': flood_event, 'status': 'no_valid_times'}
        
        # 创建输出目录
        output_dir = os.path.join(
            self.config.get('paths.output_dir'),
            'IDW',
            flood_event
        )
        os.makedirs(output_dir, exist_ok=True)
        
        # 初始化IDW插值器
        idw_config = self.config.get_method_config('IDW')
        interpolator = IDWInterpolator(
            power=idw_config.get('power', 2.0),
            search_radius=idw_config.get('search_radius', 0.5),
            min_neighbors=idw_config.get('min_neighbors', 3),
            max_neighbors=idw_config.get('max_neighbors', 12),
            use_anisotropy=idw_config.get('use_anisotropy', False),
            anisotropy_angle=idw_config.get('anisotropy_angle', 0.0),
            anisotropy_ratio=idw_config.get('anisotropy_ratio', 1.0),
            use_parallel=self.config.get('parallel.enable_parallel', True),
            chunk_size=self.config.get('parallel.chunk_size', 1000)
        )
        
        # 处理每个待插值站点
        event_results = []
        
        for _, row in interp_info.iterrows():
            target_station = row['target_station']
            target_name = row['target_station_name']
            target_lon = row['target_longitude']
            target_lat = row['target_latitude']
            
            self.logger.info(f"处理待插值站点: {target_name} ({target_station})")
            
            # 解析插值站点
            interp_stations = self.data_processor.parse_interp_stations(
                row['interp_stations_info']
            )
            
            if len(interp_stations) < idw_config.get('min_neighbors', 3):
                self.logger.warning(f"站点 {target_station} 的插值站点数量不足")
                continue
            
            # 处理每个时刻
            station_results = self._process_station_timeseries(
                target_station, target_name, target_lon, target_lat,
                interp_stations, rainfall_data, valid_times,
                interpolator, output_dir, flood_event
            )
            
            if station_results:
                event_results.append(station_results)
        
        # 汇总结果
        result = {
            'flood_event': flood_event,
            'status': 'completed',
            'processed_stations': len(event_results),
            'station_results': event_results
        }
        
        self.logger.info(f"洪水场次 {flood_event} 处理完成，共处理 {len(event_results)} 个站点")
        return result
    
    def _process_station_timeseries(self, target_station: str, target_name: str,
                                  target_lon: float, target_lat: float,
                                  interp_stations: List[Tuple[str, str]],
                                  rainfall_data: Dict[str, pd.DataFrame],
                                  valid_times: List[datetime],
                                  interpolator: IDWInterpolator,
                                  output_dir: str,
                                  flood_event: str) -> Optional[Dict[str, Any]]:
        """
        处理单个站点的时间序列插值
        
        Args:
            target_station: 目标站点编号
            target_name: 目标站点名称
            target_lon: 目标站点经度
            target_lat: 目标站点纬度
            interp_stations: 插值站点列表
            rainfall_data: 雨量数据字典
            valid_times: 有效时刻列表
            interpolator: IDW插值器
            output_dir: 输出目录
            flood_event: 洪水场次
            
        Returns:
            站点处理结果
        """
        # 获取目标站点的观测数据
        if target_station not in rainfall_data:
            self.logger.warning(f"目标站点 {target_station} 没有观测数据")
            return None
        
        target_data = rainfall_data[target_station]
        
        # 准备插值数据
        observed_values = []
        predicted_values = []
        time_stamps = []
        
        # 获取插值站点坐标
        interp_coords = []
        interp_station_ids = []
        
        for station_name, station_id in interp_stations:
            coords = self.data_processor.get_station_coordinates(station_id)
            if coords and station_id in rainfall_data:
                interp_coords.append(coords)
                interp_station_ids.append(station_id)
        
        if len(interp_coords) < interpolator.min_neighbors:
            self.logger.warning(f"站点 {target_station} 的有效插值站点数量不足")
            return None
        
        interp_coords = np.array(interp_coords)
        
        # 处理每个时刻
        for time_point in valid_times:
            # 获取目标站点观测值
            target_obs = target_data[target_data['时间'] == time_point]
            if target_obs.empty:
                continue
            
            observed_value = target_obs['雨量'].iloc[0]
            
            # 获取插值站点的值
            interp_values = []
            valid_interp_coords = []
            
            for i, station_id in enumerate(interp_station_ids):
                station_data = rainfall_data[station_id]
                time_data = station_data[station_data['时间'] == time_point]
                
                if not time_data.empty:
                    interp_values.append(time_data['雨量'].iloc[0])
                    valid_interp_coords.append(interp_coords[i])
            
            if len(interp_values) < interpolator.min_neighbors:
                continue
            
            # 执行插值
            interp_values = np.array(interp_values)
            valid_interp_coords = np.array(valid_interp_coords)
            
            interpolator.fit(valid_interp_coords, interp_values)
            predicted_value = interpolator.predict([[target_lon, target_lat]])[0]
            
            observed_values.append(observed_value)
            predicted_values.append(predicted_value)
            time_stamps.append(time_point)
        
        if not observed_values:
            self.logger.warning(f"站点 {target_station} 没有有效的插值结果")
            return None
        
        # 计算评价指标
        observed_array = np.array(observed_values)
        predicted_array = np.array(predicted_values)
        
        metrics = self.evaluator.calculate_all_metrics(observed_array, predicted_array)
        
        # 保存结果
        results_df = pd.DataFrame({
            '时间': time_stamps,
            '观测值': observed_values,
            '插值值': predicted_values
        })
        
        # 生成安全的文件名
        safe_name = self.raster_utils.safe_filename(f"{target_name}_{target_station}")
        results_file = os.path.join(output_dir, f"{safe_name}_IDW_results.csv")
        results_df.to_csv(results_file, index=False, encoding='utf-8')
        
        # 记录结果
        station_result = {
            'station_id': target_station,
            'station_name': target_name,
            'longitude': target_lon,
            'latitude': target_lat,
            'metrics': metrics,
            'results_file': results_file,
            'valid_points': len(observed_values)
        }
        
        self.logger.info(f"站点 {target_name} 插值完成，NSE: {metrics.get('NSE', np.nan):.4f}")
        
        return station_result

    def run_all_events(self) -> List[Dict[str, Any]]:
        """
        运行所有洪水场次的IDW插值

        Returns:
            所有场次的运行结果列表
        """
        flood_events = self.data_processor.get_flood_events()
        all_results = []

        self.logger.info(f"开始处理所有洪水场次，共 {len(flood_events)} 个")

        for i, flood_event in enumerate(flood_events):
            self.logger.info(f"处理进度: {i+1}/{len(flood_events)} - {flood_event}")

            try:
                result = self.run_single_event(flood_event)
                all_results.append(result)

                # 清理内存
                if (i + 1) % self.config.get('data_processing.memory_cleanup_interval', 50) == 0:
                    self.data_processor.clear_cache()
                    self.raster_utils.clear_memory()

            except Exception as e:
                self.logger.error(f"处理洪水场次 {flood_event} 时出错: {e}")
                all_results.append({
                    'flood_event': flood_event,
                    'status': 'error',
                    'error_message': str(e)
                })

        # 保存汇总结果
        self._save_summary_results(all_results)

        return all_results

    def _save_summary_results(self, all_results: List[Dict[str, Any]]):
        """
        保存汇总结果

        Args:
            all_results: 所有结果列表
        """
        summary_data = []

        for result in all_results:
            if result['status'] == 'completed' and 'station_results' in result:
                for station_result in result['station_results']:
                    metrics = station_result['metrics']
                    summary_row = {
                        'flood_event': result['flood_event'],
                        'station_id': station_result['station_id'],
                        'station_name': station_result['station_name'],
                        'longitude': station_result['longitude'],
                        'latitude': station_result['latitude'],
                        'valid_points': station_result['valid_points'],
                        'MAE': metrics.get('MAE', np.nan),
                        'RMSE': metrics.get('RMSE', np.nan),
                        'R2': metrics.get('R2', np.nan),
                        'NSE': metrics.get('NSE', np.nan),
                        'Bias': metrics.get('Bias', np.nan),
                        'Relative_Bias': metrics.get('Relative_Bias', np.nan),
                        'Correlation': metrics.get('Correlation', np.nan)
                    }
                    summary_data.append(summary_row)

        if summary_data:
            summary_df = pd.DataFrame(summary_data)

            # 保存详细结果
            output_dir = os.path.join(self.config.get('paths.output_dir'), 'IDW')
            os.makedirs(output_dir, exist_ok=True)

            summary_file = os.path.join(output_dir, 'IDW_summary_results.csv')
            summary_df.to_csv(summary_file, index=False, encoding='utf-8')

            # 计算统计信息
            stats_data = []
            for metric in ['MAE', 'RMSE', 'R2', 'NSE', 'Bias', 'Relative_Bias', 'Correlation']:
                values = summary_df[metric].dropna()
                if len(values) > 0:
                    stats_row = {
                        'metric': metric,
                        'count': len(values),
                        'mean': values.mean(),
                        'std': values.std(),
                        'min': values.min(),
                        'max': values.max(),
                        'median': values.median()
                    }
                    stats_data.append(stats_row)

            if stats_data:
                stats_df = pd.DataFrame(stats_data)
                stats_file = os.path.join(output_dir, 'IDW_statistics.csv')
                stats_df.to_csv(stats_file, index=False, encoding='utf-8')

            self.logger.info(f"汇总结果已保存: {summary_file}")
            self.logger.info(f"统计信息已保存: {stats_file}")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='IDW插值运行器')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--flood-event', type=str, help='指定洪水场次')
    parser.add_argument('--all-events', action='store_true', help='处理所有洪水场次')

    args = parser.parse_args()

    try:
        # 创建运行器
        runner = IDWRunner(args.config)

        if args.all_events:
            # 处理所有洪水场次
            results = runner.run_all_events()
            print(f"处理完成，共处理 {len(results)} 个洪水场次")

        elif args.flood_event:
            # 处理指定洪水场次
            result = runner.run_single_event(args.flood_event)
            print(f"洪水场次 {args.flood_event} 处理完成")
            print(f"状态: {result['status']}")
            if result['status'] == 'completed':
                print(f"处理站点数: {result['processed_stations']}")

        else:
            # 根据配置文件决定运行模式
            run_mode = runner.config.get_run_mode()
            target_event = runner.config.get_target_flood_event()

            if 'all_events' in run_mode:
                results = runner.run_all_events()
                print(f"处理完成，共处理 {len(results)} 个洪水场次")
            elif target_event:
                result = runner.run_single_event(target_event)
                print(f"洪水场次 {target_event} 处理完成")
                print(f"状态: {result['status']}")
            else:
                print("请指定洪水场次或使用 --all-events 参数")

    except Exception as e:
        print(f"运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
